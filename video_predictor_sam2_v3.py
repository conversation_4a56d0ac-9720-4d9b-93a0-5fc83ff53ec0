#!/usr/bin/env python3
"""
SAM2 Video Predictor Standalone Script v3

Enhanced version with multi-checkpoint support, adjustable input size, 
video creation functionality, and directory structure management.

Features:
- Automatic video-to-frame conversion using video2frame.py
- Multiple SAM2 model size support (tiny, small, base_plus, large)
- Configurable model selection via command-line arguments
- Adjustable input size for Video Segmentation model
- Video creation functionality from processed image sequences
- Directory structure management (video_dir, mask_data_dir, json_data_dir, result_dir)
- Interactive click-based segmentation
- Bounding box support
- Multi-object tracking
- Frame-by-frame processing
- Mask visualization and export

Requirements:
- SAM2 environment with all dependencies installed
- CUDA-capable GPU (recommended)
- Proper PYTHONPATH configuration

Usage:
    PYTHONPATH="/c/Users/<USER>/Codings/sam2davis/sam2:/c/Users/<USER>/Codings/sam2davis:$PYTHONPATH" \
    "/c/Users/<USER>/.conda/envs/sam2_env_py310/python.exe" \
    video_predictor_sam2_v3.py --video path/to/video.mp4 --model base_plus --input_size 1024

Author: Enhanced from Meta's SAM2 video_predictor_example.ipynb
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path
import numpy as np
import torch
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
from matplotlib.widgets import Button, TextBox
from PIL import Image
import cv2
from tqdm import tqdm
import warnings
import shutil
import json

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")

# Environment setup for Apple MPS fallback
os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"

# Add current directory to Python path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Add Grounded-SAM-2 directory to path if it exists
grounded_sam2_dir = os.path.join(current_dir, "Grounded-SAM-2")
if os.path.exists(grounded_sam2_dir) and grounded_sam2_dir not in sys.path:
    sys.path.insert(0, grounded_sam2_dir)

# SAM2 imports
try:
    from sam2.build_sam import build_sam2_video_predictor
except ImportError as e:
    print(f"SAM2 import error: {e}")
    print("Please ensure you're running from the correct directory with proper PYTHONPATH")
    print("Expected PYTHONPATH format:")
    print('PYTHONPATH="/path/to/sam2davis/sam2:/path/to/sam2davis:$PYTHONPATH"')
    print(f"Current directory: {current_dir}")
    print(f"Grounded-SAM-2 directory: {grounded_sam2_dir}")
    print(f"Python path: {sys.path}")
    sys.exit(1)


def create_dirs(path):
    """
    Ensure the given path exists. If it does not exist, create it using os.makedirs.
    
    Args:
        path (str): The directory path to check or create.
    """
    try: 
        if not os.path.exists(path):
            os.makedirs(path, exist_ok=True)
            print(f"Path '{path}' did not exist and has been created.")
        else:
            print(f"Path '{path}' already exists.")
    except Exception as e:
        print(f"An error occurred while creating the path: {e}")


def create_video_from_images(image_folder, output_video_path, frame_rate=25):
    """
    Create a video from a sequence of images.
    
    Args:
        image_folder (str): Path to folder containing images
        output_video_path (str): Path for output video file
        frame_rate (int): Frame rate for the output video
    """
    # define valid extension
    valid_extensions = [".jpg", ".jpeg", ".JPG", ".JPEG", ".png", ".PNG"]
    
    # get all image files in the folder
    image_files = [f for f in os.listdir(image_folder) 
                   if os.path.splitext(f)[1] in valid_extensions]
    image_files.sort()  # sort the files in alphabetical order
    print(f"Found {len(image_files)} images for video creation")
    
    if not image_files:
        raise ValueError("No valid image files found in the specified folder.")
    
    # load the first image to get the dimensions of the video
    first_image_path = os.path.join(image_folder, image_files[0])
    first_image = cv2.imread(first_image_path)
    height, width, _ = first_image.shape
    
    # create a video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v') # codec for saving the video
    video_writer = cv2.VideoWriter(output_video_path, fourcc, frame_rate, (width, height))
    
    # write each image to the video
    for image_file in tqdm(image_files, desc="Creating video"):
        image_path = os.path.join(image_folder, image_file)
        image = cv2.imread(image_path)
        video_writer.write(image)
    
    # source release
    video_writer.release()
    print(f"Video saved at {output_video_path}")


class DirectoryManager:
    """Manages directory structure for video processing workflow."""
    
    def __init__(self, base_output_dir):
        """
        Initialize directory manager.
        
        Args:
            base_output_dir (str): Base output directory for all processing results
        """
        self.base_output_dir = Path(base_output_dir)
        self.video_dir = None
        self.mask_data_dir = None
        self.json_data_dir = None
        self.result_dir = None
        
    def setup_directories(self, video_name=None):
        """
        Setup directory structure for video processing.
        
        Args:
            video_name (str): Optional video name for subdirectory organization
        """
        if video_name:
            self.base_output_dir = self.base_output_dir / video_name
            
        # Create main output directory
        create_dirs(str(self.base_output_dir))
        
        # Setup subdirectories
        self.mask_data_dir = self.base_output_dir / "mask_data"
        self.json_data_dir = self.base_output_dir / "json_data"
        self.result_dir = self.base_output_dir / "result"
        
        # Create all subdirectories
        create_dirs(str(self.mask_data_dir))
        create_dirs(str(self.json_data_dir))
        create_dirs(str(self.result_dir))
        
        print(f"Directory structure created:")
        print(f"  Base: {self.base_output_dir}")
        print(f"  Masks: {self.mask_data_dir}")
        print(f"  JSON: {self.json_data_dir}")
        print(f"  Results: {self.result_dir}")
        
    def set_video_dir(self, video_dir):
        """Set the video directory path."""
        self.video_dir = Path(video_dir)
        print(f"Video directory set to: {self.video_dir}")
        
    def get_paths(self):
        """Get all directory paths as a dictionary."""
        return {
            'base_output_dir': str(self.base_output_dir),
            'video_dir': str(self.video_dir) if self.video_dir else None,
            'mask_data_dir': str(self.mask_data_dir),
            'json_data_dir': str(self.json_data_dir),
            'result_dir': str(self.result_dir)
        }


class SAM2VideoPredictor:
    """Enhanced SAM2 Video Predictor class with configurable model support."""
    
    def __init__(self, model_size="base_plus", device=None, input_size=None):
        """
        Initialize the SAM2 Video Predictor.
        
        Args:
            model_size (str): Model size - tiny, small, base_plus, or large
            device (str): Device to use - cuda, cpu, or mps
            input_size (int): Input size for the model (optional)
        """
        self.model_size = model_size
        self.device = self._setup_device(device)
        self.input_size = input_size
        self.predictor = None
        self.inference_state = None
        
        print(f"Initializing SAM2 Video Predictor...")
        print(f"Model size: {model_size}")
        print(f"Device: {self.device}")
        if input_size:
            print(f"Input size: {input_size}")
        
        # Load the model
        self._load_model()
    
    def _setup_device(self, device=None):
        """Setup computation device with proper configuration."""
        if device is None:
            if torch.cuda.is_available():
                device = torch.device("cuda")
            elif torch.backends.mps.is_available():
                device = torch.device("mps")
            else:
                device = torch.device("cpu")
        else:
            device = torch.device(device)
        
        print(f"Using device: {device}")
        
        # Configure device-specific optimizations
        if device.type == "cuda":
            # Use bfloat16 for CUDA
            torch.autocast("cuda", dtype=torch.bfloat16).__enter__()
            # Enable TF32 for Ampere GPUs
            if torch.cuda.get_device_properties(0).major >= 8:
                torch.backends.cuda.matmul.allow_tf32 = True
                torch.backends.cudnn.allow_tf32 = True
                print("Enabled TF32 for Ampere GPU")
        elif device.type == "mps":
            print("Warning: MPS support is preliminary. SAM2 is trained with CUDA and might")
            print("give numerically different outputs and sometimes degraded performance on MPS.")
        
        return device

    def _find_config_and_checkpoint_paths(self):
        """Find the correct paths for SAM2 configs and checkpoints."""
        # Possible config base paths
        possible_config_bases = [
            "configs/sam2.1",  # Current directory
            "sam2/configs/sam2.1",  # sam2 subdirectory
            "../configs/sam2.1",  # Parent directory
            "Grounded-SAM-2/configs/sam2.1",  # Grounded-SAM-2 subdirectory
        ]

        # Possible checkpoint base paths
        possible_checkpoint_bases = [
            "checkpoints",  # Current directory
            "../checkpoints",  # Parent directory
            "Grounded-SAM-2/checkpoints",  # Grounded-SAM-2 subdirectory
        ]

        # Find working config path
        config_base = None
        for path in possible_config_bases:
            if os.path.exists(path):
                config_base = path
                break

        # Find working checkpoint path
        checkpoint_base = None
        for path in possible_checkpoint_bases:
            if os.path.exists(path):
                checkpoint_base = path
                break

        if not config_base:
            raise RuntimeError(f"Could not find SAM2 configs. Searched: {possible_config_bases}")
        if not checkpoint_base:
            raise RuntimeError(f"Could not find SAM2 checkpoints. Searched: {possible_checkpoint_bases}")

        print(f"Found config base: {config_base}")
        print(f"Found checkpoint base: {checkpoint_base}")

        return config_base, checkpoint_base

    def _load_model(self):
        """Load the SAM2 video predictor model with configurable checkpoint support."""
        # Use the simplified approach that works with the current environment
        # Model configurations using relative paths from the Grounded-SAM-2 working directory
        model_configs = {
            "tiny": ("configs/sam2.1/sam2.1_hiera_t.yaml", "checkpoints/sam2.1_hiera_tiny.pt"),
            "small": ("configs/sam2.1/sam2.1_hiera_s.yaml", "checkpoints/sam2.1_hiera_small.pt"),
            "base_plus": ("configs/sam2.1/sam2.1_hiera_b+.yaml", "checkpoints/sam2.1_hiera_base_plus.pt"),
            "large": ("configs/sam2.1/sam2.1_hiera_l.yaml", "checkpoints/sam2.1_hiera_large.pt")
        }

        if self.model_size not in model_configs:
            raise ValueError(f"Invalid model size: {self.model_size}. Choose from: {list(model_configs.keys())}")

        model_cfg, checkpoint_path = model_configs[self.model_size]

        # Check if we need to change working directory to find configs
        original_cwd = os.getcwd()
        grounded_sam2_path = os.path.join(current_dir, "Grounded-SAM-2")

        # Try to find configs in multiple locations
        config_found = False
        checkpoint_found = False

        # Check current directory first
        if os.path.exists(model_cfg) and os.path.exists(checkpoint_path):
            config_found = True
            checkpoint_found = True
            print(f"Found configs in current directory")

        # Check Grounded-SAM-2 directory
        elif os.path.exists(grounded_sam2_path):
            grounded_config = os.path.join(grounded_sam2_path, model_cfg)
            grounded_checkpoint = os.path.join(grounded_sam2_path, checkpoint_path)

            if os.path.exists(grounded_config):
                config_found = True
                model_cfg = grounded_config
                print(f"Found config in Grounded-SAM-2: {grounded_config}")

            if os.path.exists(grounded_checkpoint):
                checkpoint_found = True
                checkpoint_path = grounded_checkpoint
                print(f"Found checkpoint in Grounded-SAM-2: {grounded_checkpoint}")

        # Check parent directory for checkpoints
        if not checkpoint_found:
            parent_checkpoint = os.path.join(current_dir, checkpoint_path)
            if os.path.exists(parent_checkpoint):
                checkpoint_found = True
                checkpoint_path = parent_checkpoint
                print(f"Found checkpoint in parent directory: {parent_checkpoint}")

        if not config_found:
            raise FileNotFoundError(f"Config file not found: {model_cfg}")
        if not checkpoint_found:
            raise FileNotFoundError(f"Checkpoint file not found: {checkpoint_path}")

        print(f"Loading model config: {model_cfg}")
        print(f"Loading checkpoint: {checkpoint_path}")

        # Build the video predictor with optional input size configuration
        if self.input_size:
            # Note: Input size configuration would need to be implemented in the SAM2 config
            # For now, we'll load the standard model and note the input size preference
            print(f"Note: Input size {self.input_size} requested - this may require config modification")

        # Change to Grounded-SAM-2 directory if needed for Hydra to find configs
        try:
            if os.path.exists(grounded_sam2_path) and not config_found:
                print(f"Changing working directory to: {grounded_sam2_path}")
                os.chdir(grounded_sam2_path)
                # Use relative paths from Grounded-SAM-2 directory
                model_cfg = model_configs[self.model_size][0]
                checkpoint_path = os.path.relpath(checkpoint_path, grounded_sam2_path)

            print(f"Using config path: {model_cfg}")
            print(f"Using checkpoint path: {checkpoint_path}")

            self.predictor = build_sam2_video_predictor(model_cfg, checkpoint_path, device=self.device)
            print("SAM2 video predictor loaded successfully!")

        finally:
            # Always restore original working directory
            os.chdir(original_cwd)

    def get_model_info(self):
        """Get information about the loaded model."""
        return {
            'model_size': self.model_size,
            'device': str(self.device),
            'input_size': self.input_size,
            'model_loaded': self.predictor is not None
        }

    def preprocess_video(self, video_path, max_frames=100, output_dir=None):
        """
        Convert video to frames using the video2frame.py script.

        Args:
            video_path (str): Path to the input video file
            max_frames (int): Maximum number of frames to extract
            output_dir (str): Output directory for frames (optional)

        Returns:
            str: Path to the directory containing extracted frames
        """
        video_path = Path(video_path)

        if not video_path.exists():
            raise FileNotFoundError(f"Video file not found: {video_path}")

        print(f"Preprocessing video: {video_path}")

        # Find video2frame.py script
        video2frame_script = None
        possible_paths = [
            "video2frame.py",  # Current directory
            "../video2frame.py",  # Parent directory
            os.path.join(current_dir, "video2frame.py"),  # Script directory
            os.path.join(os.path.dirname(current_dir), "video2frame.py"),  # Parent of script directory
        ]

        for path in possible_paths:
            if os.path.exists(path):
                video2frame_script = path
                break

        if not video2frame_script:
            raise FileNotFoundError(f"video2frame.py script not found. Searched: {possible_paths}")

        # Prepare video2frame command
        cmd = [
            sys.executable, video2frame_script,
            "--input", str(video_path),
            "--max-frames", str(max_frames)
        ]

        if output_dir:
            cmd.extend(["--output_dir", output_dir])

        # Run video2frame.py
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print("Video preprocessing completed successfully!")
            print(result.stdout)
        except subprocess.CalledProcessError as e:
            print(f"Error during video preprocessing: {e}")
            print(f"stdout: {e.stdout}")
            print(f"stderr: {e.stderr}")
            raise

        # Determine output directory
        if output_dir:
            frames_dir = Path(output_dir) / f"{video_path.stem}_frames"
        else:
            frames_dir = video_path.parent / f"{video_path.stem}_frames"

        if not frames_dir.exists():
            raise RuntimeError(f"Expected frames directory not found: {frames_dir}")

        print(f"Frames extracted to: {frames_dir}")
        return str(frames_dir)

    def init_video_state(self, video_frames_dir):
        """
        Initialize inference state for the video.

        Args:
            video_frames_dir (str): Directory containing video frames

        Returns:
            dict: Inference state for the video
        """
        print(f"Initializing video state for: {video_frames_dir}")

        # Store for reset functionality
        self.video_frames_dir = video_frames_dir

        # Initialize inference state
        self.inference_state = self.predictor.init_state(video_path=video_frames_dir)

        print("Video state initialized successfully!")
        return self.inference_state

    def add_click_annotation(self, frame_idx, points, labels, obj_id=1):
        """
        Add click annotations to a frame.

        Args:
            frame_idx (int): Frame index (0-based)
            points (list): List of [x, y] coordinates
            labels (list): List of labels (1 for positive, 0 for negative)
            obj_id (int): Object ID for tracking

        Returns:
            tuple: (out_obj_ids, out_mask_logits) from the predictor
        """
        if self.inference_state is None:
            raise RuntimeError("Video state not initialized. Call init_video_state() first.")

        points = np.array(points, dtype=np.float32)
        labels = np.array(labels, dtype=np.int32)

        print(f"Adding click annotation to frame {frame_idx}")
        print(f"Points: {points}")
        print(f"Labels: {labels}")
        print(f"Object ID: {obj_id}")

        # Debug: Check for negative clicks
        positive_count = np.sum(labels == 1)
        negative_count = np.sum(labels == 0)
        print(f"Debug: {positive_count} positive clicks, {negative_count} negative clicks")

        # Debug: Show which points are negative
        for i, (point, label) in enumerate(zip(points, labels)):
            label_text = "positive" if label == 1 else "negative"
            print(f"  Point {i+1}: ({point[0]:.1f}, {point[1]:.1f}) - {label_text}")

        # Add points to the predictor
        _, out_obj_ids, out_mask_logits = self.predictor.add_new_points_or_box(
            inference_state=self.inference_state,
            frame_idx=frame_idx,
            obj_id=obj_id,
            points=points,
            labels=labels,
        )

        # Debug: Check mask logits statistics
        if len(out_mask_logits) > 0:
            mask_logits = out_mask_logits[0]  # First object's mask
            mask_min = float(mask_logits.min())
            mask_max = float(mask_logits.max())
            mask_mean = float(mask_logits.mean())
            print(f"Debug: Mask logits - min: {mask_min:.3f}, max: {mask_max:.3f}, mean: {mask_mean:.3f}")

            # Check if negative areas have lower logits
            if negative_count > 0:
                print("Debug: Negative clicks should result in lower mask logits in those areas")

        return out_obj_ids, out_mask_logits

    def add_box_annotation(self, frame_idx, box, obj_id=1):
        """
        Add bounding box annotation to a frame.

        Args:
            frame_idx (int): Frame index (0-based)
            box (list): Bounding box as [x1, y1, x2, y2]
            obj_id (int): Object ID for tracking

        Returns:
            tuple: (out_obj_ids, out_mask_logits) from the predictor
        """
        if self.inference_state is None:
            raise RuntimeError("Video state not initialized. Call init_video_state() first.")

        box = np.array(box, dtype=np.float32)

        print(f"Adding box annotation to frame {frame_idx}")
        print(f"Box: {box}")
        print(f"Object ID: {obj_id}")

        # Add box to the predictor
        _, out_obj_ids, out_mask_logits = self.predictor.add_new_points_or_box(
            inference_state=self.inference_state,
            frame_idx=frame_idx,
            obj_id=obj_id,
            box=box,
        )

        return out_obj_ids, out_mask_logits

    def reset_state(self):
        """Reset the video state to allow new object IDs."""
        print("Resetting video state to allow new object IDs...")
        self.inference_state = None
        if hasattr(self, 'video_frames_dir') and self.video_frames_dir:
            try:
                self.init_video_state(self.video_frames_dir)
                print("Video state reset successfully.")
            except Exception as e:
                print(f"Warning: Failed to reinitialize video state: {e}")
                print("You will need to call init_video_state() manually with a valid video path.")
        else:
            print("Warning: No video frames directory stored. Call init_video_state() manually.")

    def propagate_masks(self):
        """
        Propagate masks throughout the video.

        Returns:
            dict: Dictionary mapping frame indices to (obj_ids, mask_logits)
        """
        if self.inference_state is None:
            raise RuntimeError("Video state not initialized. Call init_video_state() first.")

        print("Propagating masks throughout the video...")

        # Propagate masks to all frames
        video_segments = {}
        for out_frame_idx, out_obj_ids, out_mask_logits in self.predictor.propagate_in_video(self.inference_state):
            video_segments[out_frame_idx] = {
                'obj_ids': out_obj_ids,
                'mask_logits': out_mask_logits
            }

        print(f"Propagation completed for {len(video_segments)} frames")
        print("Note: After propagation, no new object IDs can be added. Use 'reset_state' to restart.")
        return video_segments

    def visualize_frame_with_masks(self, frame_path, masks_data, output_path=None, show_plot=True):
        """
        Visualize a frame with overlaid masks.

        Args:
            frame_path (str): Path to the frame image
            masks_data (dict): Dictionary containing 'obj_ids' and 'mask_logits'
            output_path (str): Path to save the visualization (optional)
            show_plot (bool): Whether to display the plot

        Returns:
            matplotlib.figure.Figure: The created figure
        """
        # Load the frame
        frame = Image.open(frame_path)
        frame_array = np.array(frame)

        # Create figure
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.imshow(frame_array)
        ax.set_title(f"Frame: {Path(frame_path).name}")

        # Overlay masks
        if masks_data and 'mask_logits' in masks_data:
            obj_ids = masks_data['obj_ids']
            mask_logits = masks_data['mask_logits']

            for i, obj_id in enumerate(obj_ids):
                mask = (mask_logits[i] > 0.0).cpu().numpy()
                self._show_mask(mask, ax, obj_id=obj_id)

        ax.axis('off')

        # Save if output path provided
        if output_path:
            plt.savefig(output_path, bbox_inches='tight', dpi=150)
            print(f"Visualization saved to: {output_path}")

        # Show plot if requested
        if show_plot:
            plt.show()

        return fig

    def _show_mask(self, mask, ax, obj_id=None, random_color=False):
        """
        Display mask overlay on the given axes.

        Args:
            mask (np.ndarray): Binary mask array
            ax (matplotlib.axes.Axes): Matplotlib axes to draw on
            obj_id (int): Object ID for color selection
            random_color (bool): Whether to use random colors
        """
        if random_color:
            color = np.concatenate([np.random.random(3), np.array([0.6])], axis=0)
        else:
            # Use a more diverse color palette and ensure unique colors per object ID
            colors = [
                [1.0, 0.0, 0.0],  # Red
                [0.0, 1.0, 0.0],  # Green
                [0.0, 0.0, 1.0],  # Blue
                [1.0, 1.0, 0.0],  # Yellow
                [1.0, 0.0, 1.0],  # Magenta
                [0.0, 1.0, 1.0],  # Cyan
                [1.0, 0.5, 0.0],  # Orange
                [0.5, 0.0, 1.0],  # Purple
                [0.0, 0.5, 0.0],  # Dark Green
                [0.5, 0.5, 0.5],  # Gray
            ]

            if obj_id is None:
                color_rgb = colors[0]
            else:
                # Use modulo to cycle through colors, but ensure different objects get different colors
                color_idx = (obj_id - 1) % len(colors)
                color_rgb = colors[color_idx]

            color = np.array([*color_rgb, 0.6])  # Add alpha

        h, w = mask.shape[-2:]
        mask_image = mask.reshape(h, w, 1) * color.reshape(1, 1, -1)
        ax.imshow(mask_image)

    def export_masks(self, video_segments, output_dir):
        """
        Export masks as PNG files.

        Args:
            video_segments (dict): Video segments from propagate_masks()
            output_dir (str): Directory to save mask files
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        print(f"Exporting masks to: {output_dir}")

        for frame_idx, masks_data in tqdm(video_segments.items(), desc="Exporting masks"):
            if 'mask_logits' in masks_data:
                obj_ids = masks_data['obj_ids']
                mask_logits = masks_data['mask_logits']

                for i, obj_id in enumerate(obj_ids):
                    mask = (mask_logits[i] > 0.0).cpu().numpy().squeeze()

                    # Convert to uint8
                    mask_uint8 = (mask * 255).astype(np.uint8)

                    # Save mask
                    mask_filename = f"frame_{frame_idx:05d}_obj_{obj_id:02d}_mask.png"
                    mask_path = output_dir / mask_filename

                    Image.fromarray(mask_uint8).save(mask_path)

        print(f"Masks exported successfully!")

    def export_masks_with_overlay(self, video_segments, frames_dir, output_dir):
        """
        Export masks with overlay on original frames and create video.

        Args:
            video_segments (dict): Video segments from propagate_masks()
            frames_dir (str): Directory containing original frames
            output_dir (str): Directory to save overlaid frames
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        print(f"Exporting masks with overlay to: {output_dir}")

        # Get frame files
        frames_dir = Path(frames_dir)
        frame_files = sorted([f for f in frames_dir.glob("*") if f.suffix.lower() in ['.jpg', '.jpeg', '.png']])

        for frame_idx, masks_data in tqdm(video_segments.items(), desc="Creating overlay frames"):
            if frame_idx < len(frame_files):
                frame_path = frame_files[frame_idx]
                output_path = output_dir / f"overlay_{frame_path.name}"

                # Create visualization without showing
                self.visualize_frame_with_masks(
                    str(frame_path),
                    masks_data,
                    output_path=str(output_path),
                    show_plot=False
                )
                plt.close()  # Close figure to save memory

        print(f"Overlay frames exported successfully!")
        return str(output_dir)


def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="SAM2 Video Predictor v3 - Enhanced video segmentation with configurable models",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic usage with default model
  python video_predictor_sam2_v3.py --video input.mp4

  # Use specific model size
  python video_predictor_sam2_v3.py --video input.mp4 --model tiny

  # Specify input size and output directory
  python video_predictor_sam2_v3.py --video input.mp4 --model base_plus --input_size 1024 --output_dir ./results

  # Create video from processed frames
  python video_predictor_sam2_v3.py --video input.mp4 --create_video --frame_rate 30
        """
    )

    # Required arguments
    parser.add_argument(
        "--video",
        type=str,
        required=True,
        help="Path to input video file"
    )

    # Model configuration
    parser.add_argument(
        "--model",
        type=str,
        choices=["tiny", "small", "base_plus", "large"],
        default="base_plus",
        help="SAM2 model size to use (default: base_plus)"
    )

    parser.add_argument(
        "--input_size",
        type=int,
        help="Input size for the Video Segmentation model (optional)"
    )

    # Processing options
    parser.add_argument(
        "--max_frames",
        type=int,
        default=100,
        help="Maximum number of frames to extract from video (default: 100)"
    )

    parser.add_argument(
        "--output_dir",
        type=str,
        default="./sam2_output",
        help="Output directory for all results (default: ./sam2_output)"
    )

    # Video creation options
    parser.add_argument(
        "--create_video",
        action="store_true",
        help="Create video from processed frames"
    )

    parser.add_argument(
        "--frame_rate",
        type=int,
        default=25,
        help="Frame rate for output video (default: 25)"
    )

    # Device options
    parser.add_argument(
        "--device",
        type=str,
        choices=["cuda", "cpu", "mps"],
        help="Device to use for computation (auto-detect if not specified)"
    )

    # Interactive mode
    parser.add_argument(
        "--interactive",
        action="store_true",
        help="Enable interactive annotation mode"
    )

    return parser.parse_args()


def main():
    """Main function for the enhanced SAM2 video predictor."""
    args = parse_arguments()

    print("=" * 80)
    print("SAM2 Video Predictor v3 - Enhanced Edition")
    print("=" * 80)
    print(f"Video: {args.video}")
    print(f"Model: {args.model}")
    print(f"Input size: {args.input_size}")
    print(f"Max frames: {args.max_frames}")
    print(f"Output directory: {args.output_dir}")
    print(f"Device: {args.device}")
    print(f"Interactive mode: {args.interactive}")
    print("=" * 80)

    try:
        # Initialize directory manager
        video_name = Path(args.video).stem
        dir_manager = DirectoryManager(args.output_dir)
        dir_manager.setup_directories(video_name)

        # Initialize SAM2 predictor
        predictor = SAM2VideoPredictor(
            model_size=args.model,
            device=args.device,
            input_size=args.input_size
        )

        # Print model information
        model_info = predictor.get_model_info()
        print(f"\nModel Information:")
        for key, value in model_info.items():
            print(f"  {key}: {value}")

        # Preprocess video to frames
        print(f"\nPreprocessing video...")
        frames_dir = predictor.preprocess_video(
            args.video,
            max_frames=args.max_frames,
            output_dir=str(dir_manager.base_output_dir)
        )
        dir_manager.set_video_dir(frames_dir)

        # Initialize video state
        print(f"\nInitializing video state...")
        predictor.init_video_state(frames_dir)

        if args.interactive:
            print(f"\nStarting interactive annotation mode...")
            print("This would launch the interactive interface.")
            print("Interactive mode implementation would go here.")
            # TODO: Implement interactive annotation interface from v2
        else:
            print(f"\nNon-interactive mode - basic processing completed.")
            print("For interactive annotation, use --interactive flag.")

        # Test video creation functionality if requested
        if args.create_video:
            print(f"\nTesting video creation functionality...")
            frames_dir = dir_manager.get_paths()['video_dir']
            if frames_dir and os.path.exists(frames_dir):
                output_video_path = str(dir_manager.base_output_dir / f"{video_name}_test.mp4")
                try:
                    create_video_from_images(frames_dir, output_video_path, args.frame_rate)
                    print(f"✅ Video creation test successful: {output_video_path}")
                except Exception as e:
                    print(f"❌ Video creation test failed: {e}")
            else:
                print("❌ No frames directory found for video creation test")

        # Get directory paths for reference
        paths = dir_manager.get_paths()
        print(f"\nDirectory structure created:")
        for key, path in paths.items():
            if path:
                print(f"  {key}: {path}")

        print(f"\nProcessing completed successfully!")
        print(f"Use the interactive mode (--interactive) to add annotations and generate masks.")

    except Exception as e:
        print(f"\nError during processing: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
