#!/usr/bin/env python3
"""
SAM2 Video Predictor Standalone Script v3

Enhanced version with multi-checkpoint support, adjustable input size, 
video creation functionality, and directory structure management.

Features:
- Automatic video-to-frame conversion using video2frame.py
- Multiple SAM2 model size support (tiny, small, base_plus, large)
- Configurable model selection via command-line arguments
- Adjustable input size for Video Segmentation model
- Video creation functionality from processed image sequences
- Directory structure management (video_dir, mask_data_dir, json_data_dir, result_dir)
- Interactive click-based segmentation
- Bounding box support
- Multi-object tracking
- Frame-by-frame processing
- Mask visualization and export

Requirements:
- SAM2 environment with all dependencies installed
- CUDA-capable GPU (recommended)
- Proper PYTHONPATH configuration

Usage:
    PYTHONPATH="/c/Users/<USER>/Codings/sam2davis/sam2:/c/Users/<USER>/Codings/sam2davis:$PYTHONPATH" \
    "/c/Users/<USER>/.conda/envs/sam2_env_py310/python.exe" \
    video_predictor_sam2_v3.py --video path/to/video.mp4 --model base_plus --input_size 1024

Author: Enhanced from Meta's SAM2 video_predictor_example.ipynb
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path
import numpy as np
import torch
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
from matplotlib.widgets import Button, TextBox
from PIL import Image
import cv2
from tqdm import tqdm
import warnings
import shutil
import json

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")

# Environment setup for Apple MPS fallback
os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"

# Add current directory to Python path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Add Grounded-SAM-2 directory to path if it exists
grounded_sam2_dir = os.path.join(current_dir, "Grounded-SAM-2")
if os.path.exists(grounded_sam2_dir) and grounded_sam2_dir not in sys.path:
    sys.path.insert(0, grounded_sam2_dir)

# SAM2 imports
try:
    from sam2.build_sam import build_sam2_video_predictor
except ImportError as e:
    print(f"SAM2 import error: {e}")
    print("Please ensure you're running from the correct directory with proper PYTHONPATH")
    print("Expected PYTHONPATH format:")
    print('PYTHONPATH="/path/to/sam2davis/sam2:/path/to/sam2davis:$PYTHONPATH"')
    print(f"Current directory: {current_dir}")
    print(f"Grounded-SAM-2 directory: {grounded_sam2_dir}")
    print(f"Python path: {sys.path}")
    sys.exit(1)


def create_dirs(path):
    """
    Ensure the given path exists. If it does not exist, create it using os.makedirs.
    
    Args:
        path (str): The directory path to check or create.
    """
    try: 
        if not os.path.exists(path):
            os.makedirs(path, exist_ok=True)
            print(f"Path '{path}' did not exist and has been created.")
        else:
            print(f"Path '{path}' already exists.")
    except Exception as e:
        print(f"An error occurred while creating the path: {e}")


def create_video_from_images(image_folder, output_video_path, frame_rate=25):
    """
    Create a video from a sequence of images.
    
    Args:
        image_folder (str): Path to folder containing images
        output_video_path (str): Path for output video file
        frame_rate (int): Frame rate for the output video
    """
    # define valid extension
    valid_extensions = [".jpg", ".jpeg", ".JPG", ".JPEG", ".png", ".PNG"]
    
    # get all image files in the folder
    image_files = [f for f in os.listdir(image_folder) 
                   if os.path.splitext(f)[1] in valid_extensions]
    image_files.sort()  # sort the files in alphabetical order
    print(f"Found {len(image_files)} images for video creation")
    
    if not image_files:
        raise ValueError("No valid image files found in the specified folder.")
    
    # load the first image to get the dimensions of the video
    first_image_path = os.path.join(image_folder, image_files[0])
    first_image = cv2.imread(first_image_path)
    height, width, _ = first_image.shape
    
    # create a video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v') # codec for saving the video
    video_writer = cv2.VideoWriter(output_video_path, fourcc, frame_rate, (width, height))
    
    # write each image to the video
    for image_file in tqdm(image_files, desc="Creating video"):
        image_path = os.path.join(image_folder, image_file)
        image = cv2.imread(image_path)
        video_writer.write(image)
    
    # source release
    video_writer.release()
    print(f"Video saved at {output_video_path}")


class DirectoryManager:
    """Manages directory structure for video processing workflow."""
    
    def __init__(self, base_output_dir):
        """
        Initialize directory manager.
        
        Args:
            base_output_dir (str): Base output directory for all processing results
        """
        self.base_output_dir = Path(base_output_dir)
        self.video_dir = None
        self.mask_data_dir = None
        self.json_data_dir = None
        self.result_dir = None
        
    def setup_directories(self, video_name=None):
        """
        Setup directory structure for video processing.
        
        Args:
            video_name (str): Optional video name for subdirectory organization
        """
        if video_name:
            self.base_output_dir = self.base_output_dir / video_name
            
        # Create main output directory
        create_dirs(str(self.base_output_dir))
        
        # Setup subdirectories
        self.mask_data_dir = self.base_output_dir / "mask_data"
        self.json_data_dir = self.base_output_dir / "json_data"
        self.result_dir = self.base_output_dir / "result"
        
        # Create all subdirectories
        create_dirs(str(self.mask_data_dir))
        create_dirs(str(self.json_data_dir))
        create_dirs(str(self.result_dir))
        
        print(f"Directory structure created:")
        print(f"  Base: {self.base_output_dir}")
        print(f"  Masks: {self.mask_data_dir}")
        print(f"  JSON: {self.json_data_dir}")
        print(f"  Results: {self.result_dir}")
        
    def set_video_dir(self, video_dir):
        """Set the video directory path."""
        self.video_dir = Path(video_dir)
        print(f"Video directory set to: {self.video_dir}")
        
    def get_paths(self):
        """Get all directory paths as a dictionary."""
        return {
            'base_output_dir': str(self.base_output_dir),
            'video_dir': str(self.video_dir) if self.video_dir else None,
            'mask_data_dir': str(self.mask_data_dir),
            'json_data_dir': str(self.json_data_dir),
            'result_dir': str(self.result_dir)
        }


class SAM2VideoPredictor:
    """Enhanced SAM2 Video Predictor class with configurable model support."""
    
    def __init__(self, model_size="base_plus", device=None, input_size=None):
        """
        Initialize the SAM2 Video Predictor.
        
        Args:
            model_size (str): Model size - tiny, small, base_plus, or large
            device (str): Device to use - cuda, cpu, or mps
            input_size (int): Input size for the model (optional)
        """
        self.model_size = model_size
        self.device = self._setup_device(device)
        self.input_size = input_size
        self.predictor = None
        self.inference_state = None
        
        print(f"Initializing SAM2 Video Predictor...")
        print(f"Model size: {model_size}")
        print(f"Device: {self.device}")
        if input_size:
            print(f"Input size: {input_size}")
        
        # Load the model
        self._load_model()
    
    def _setup_device(self, device=None):
        """Setup computation device with proper configuration."""
        if device is None:
            if torch.cuda.is_available():
                device = torch.device("cuda")
            elif torch.backends.mps.is_available():
                device = torch.device("mps")
            else:
                device = torch.device("cpu")
        else:
            device = torch.device(device)
        
        print(f"Using device: {device}")
        
        # Configure device-specific optimizations
        if device.type == "cuda":
            # Use bfloat16 for CUDA
            torch.autocast("cuda", dtype=torch.bfloat16).__enter__()
            # Enable TF32 for Ampere GPUs
            if torch.cuda.get_device_properties(0).major >= 8:
                torch.backends.cuda.matmul.allow_tf32 = True
                torch.backends.cudnn.allow_tf32 = True
                print("Enabled TF32 for Ampere GPU")
        elif device.type == "mps":
            print("Warning: MPS support is preliminary. SAM2 is trained with CUDA and might")
            print("give numerically different outputs and sometimes degraded performance on MPS.")
        
        return device

    def _find_config_and_checkpoint_paths(self):
        """Find the correct paths for SAM2 configs and checkpoints."""
        # Possible config base paths
        possible_config_bases = [
            "configs/sam2.1",  # Current directory
            "sam2/configs/sam2.1",  # sam2 subdirectory
            "../configs/sam2.1",  # Parent directory
            "Grounded-SAM-2/configs/sam2.1",  # Grounded-SAM-2 subdirectory
        ]

        # Possible checkpoint base paths
        possible_checkpoint_bases = [
            "checkpoints",  # Current directory
            "../checkpoints",  # Parent directory
            "Grounded-SAM-2/checkpoints",  # Grounded-SAM-2 subdirectory
        ]

        # Find working config path
        config_base = None
        for path in possible_config_bases:
            if os.path.exists(path):
                config_base = path
                break

        # Find working checkpoint path
        checkpoint_base = None
        for path in possible_checkpoint_bases:
            if os.path.exists(path):
                checkpoint_base = path
                break

        if not config_base:
            raise RuntimeError(f"Could not find SAM2 configs. Searched: {possible_config_bases}")
        if not checkpoint_base:
            raise RuntimeError(f"Could not find SAM2 checkpoints. Searched: {possible_checkpoint_bases}")

        print(f"Found config base: {config_base}")
        print(f"Found checkpoint base: {checkpoint_base}")

        return config_base, checkpoint_base

    def _load_model(self):
        """Load the SAM2 video predictor model with configurable checkpoint support."""
        # Use the simplified approach that works with the current environment
        # Model configurations using relative paths from the Grounded-SAM-2 working directory
        model_configs = {
            "tiny": ("configs/sam2.1/sam2.1_hiera_t.yaml", "checkpoints/sam2.1_hiera_tiny.pt"),
            "small": ("configs/sam2.1/sam2.1_hiera_s.yaml", "checkpoints/sam2.1_hiera_small.pt"),
            "base_plus": ("configs/sam2.1/sam2.1_hiera_b+.yaml", "checkpoints/sam2.1_hiera_base_plus.pt"),
            "large": ("configs/sam2.1/sam2.1_hiera_l.yaml", "checkpoints/sam2.1_hiera_large.pt")
        }

        if self.model_size not in model_configs:
            raise ValueError(f"Invalid model size: {self.model_size}. Choose from: {list(model_configs.keys())}")

        model_cfg, checkpoint_path = model_configs[self.model_size]

        # Check if we need to change working directory to find configs
        original_cwd = os.getcwd()
        grounded_sam2_path = os.path.join(current_dir, "Grounded-SAM-2")

        # Try to find configs in multiple locations
        config_found = False
        checkpoint_found = False

        # Check current directory first
        if os.path.exists(model_cfg) and os.path.exists(checkpoint_path):
            config_found = True
            checkpoint_found = True
            print(f"Found configs in current directory")

        # Check Grounded-SAM-2 directory
        elif os.path.exists(grounded_sam2_path):
            grounded_config = os.path.join(grounded_sam2_path, model_cfg)
            grounded_checkpoint = os.path.join(grounded_sam2_path, checkpoint_path)

            if os.path.exists(grounded_config):
                config_found = True
                model_cfg = grounded_config
                print(f"Found config in Grounded-SAM-2: {grounded_config}")

            if os.path.exists(grounded_checkpoint):
                checkpoint_found = True
                checkpoint_path = grounded_checkpoint
                print(f"Found checkpoint in Grounded-SAM-2: {grounded_checkpoint}")

        # Check parent directory for checkpoints
        if not checkpoint_found:
            parent_checkpoint = os.path.join(current_dir, checkpoint_path)
            if os.path.exists(parent_checkpoint):
                checkpoint_found = True
                checkpoint_path = parent_checkpoint
                print(f"Found checkpoint in parent directory: {parent_checkpoint}")

        if not config_found:
            raise FileNotFoundError(f"Config file not found: {model_cfg}")
        if not checkpoint_found:
            raise FileNotFoundError(f"Checkpoint file not found: {checkpoint_path}")

        print(f"Loading model config: {model_cfg}")
        print(f"Loading checkpoint: {checkpoint_path}")

        # Build the video predictor with optional input size configuration
        if self.input_size:
            # Note: Input size configuration would need to be implemented in the SAM2 config
            # For now, we'll load the standard model and note the input size preference
            print(f"Note: Input size {self.input_size} requested - this may require config modification")

        # Change to Grounded-SAM-2 directory if needed for Hydra to find configs
        try:
            if os.path.exists(grounded_sam2_path) and not config_found:
                print(f"Changing working directory to: {grounded_sam2_path}")
                os.chdir(grounded_sam2_path)
                # Use relative paths from Grounded-SAM-2 directory
                model_cfg = model_configs[self.model_size][0]
                checkpoint_path = os.path.relpath(checkpoint_path, grounded_sam2_path)

            print(f"Using config path: {model_cfg}")
            print(f"Using checkpoint path: {checkpoint_path}")

            self.predictor = build_sam2_video_predictor(model_cfg, checkpoint_path, device=self.device)
            print("SAM2 video predictor loaded successfully!")

        finally:
            # Always restore original working directory
            os.chdir(original_cwd)

    def get_model_info(self):
        """Get information about the loaded model."""
        return {
            'model_size': self.model_size,
            'device': str(self.device),
            'input_size': self.input_size,
            'model_loaded': self.predictor is not None
        }

    def preprocess_video(self, video_path, max_frames=100, output_dir=None):
        """
        Convert video to frames using the video2frame.py script.

        Args:
            video_path (str): Path to the input video file
            max_frames (int): Maximum number of frames to extract
            output_dir (str): Output directory for frames (optional)

        Returns:
            str: Path to the directory containing extracted frames
        """
        video_path = Path(video_path)

        if not video_path.exists():
            raise FileNotFoundError(f"Video file not found: {video_path}")

        print(f"Preprocessing video: {video_path}")

        # Find video2frame.py script
        video2frame_script = None
        possible_paths = [
            "video2frame.py",  # Current directory
            "../video2frame.py",  # Parent directory
            os.path.join(current_dir, "video2frame.py"),  # Script directory
            os.path.join(os.path.dirname(current_dir), "video2frame.py"),  # Parent of script directory
        ]

        for path in possible_paths:
            if os.path.exists(path):
                video2frame_script = path
                break

        if not video2frame_script:
            raise FileNotFoundError(f"video2frame.py script not found. Searched: {possible_paths}")

        # Prepare video2frame command
        cmd = [
            sys.executable, video2frame_script,
            "--input", str(video_path),
            "--max-frames", str(max_frames)
        ]

        if output_dir:
            cmd.extend(["--output_dir", output_dir])

        # Run video2frame.py
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print("Video preprocessing completed successfully!")
            print(result.stdout)
        except subprocess.CalledProcessError as e:
            print(f"Error during video preprocessing: {e}")
            print(f"stdout: {e.stdout}")
            print(f"stderr: {e.stderr}")
            raise

        # Determine output directory
        if output_dir:
            frames_dir = Path(output_dir) / f"{video_path.stem}_frames"
        else:
            frames_dir = video_path.parent / f"{video_path.stem}_frames"

        if not frames_dir.exists():
            raise RuntimeError(f"Expected frames directory not found: {frames_dir}")

        print(f"Frames extracted to: {frames_dir}")
        return str(frames_dir)

    def init_video_state(self, video_frames_dir):
        """
        Initialize inference state for the video.

        Args:
            video_frames_dir (str): Directory containing video frames

        Returns:
            dict: Inference state for the video
        """
        print(f"Initializing video state for: {video_frames_dir}")

        # Store for reset functionality
        self.video_frames_dir = video_frames_dir

        # Initialize inference state
        self.inference_state = self.predictor.init_state(video_path=video_frames_dir)

        print("Video state initialized successfully!")
        return self.inference_state

    def add_click_annotation(self, frame_idx, points, labels, obj_id=1):
        """
        Add click annotations to a frame.

        Args:
            frame_idx (int): Frame index (0-based)
            points (list): List of [x, y] coordinates
            labels (list): List of labels (1 for positive, 0 for negative)
            obj_id (int): Object ID for tracking

        Returns:
            tuple: (out_obj_ids, out_mask_logits) from the predictor
        """
        if self.inference_state is None:
            raise RuntimeError("Video state not initialized. Call init_video_state() first.")

        points = np.array(points, dtype=np.float32)
        labels = np.array(labels, dtype=np.int32)

        print(f"Adding click annotation to frame {frame_idx}")
        print(f"Points: {points}")
        print(f"Labels: {labels}")
        print(f"Object ID: {obj_id}")

        # Debug: Check for negative clicks
        positive_count = np.sum(labels == 1)
        negative_count = np.sum(labels == 0)
        print(f"Debug: {positive_count} positive clicks, {negative_count} negative clicks")

        # Debug: Show which points are negative
        for i, (point, label) in enumerate(zip(points, labels)):
            label_text = "positive" if label == 1 else "negative"
            print(f"  Point {i+1}: ({point[0]:.1f}, {point[1]:.1f}) - {label_text}")

        # Add points to the predictor
        _, out_obj_ids, out_mask_logits = self.predictor.add_new_points_or_box(
            inference_state=self.inference_state,
            frame_idx=frame_idx,
            obj_id=obj_id,
            points=points,
            labels=labels,
        )

        # Debug: Check mask logits statistics
        if len(out_mask_logits) > 0:
            mask_logits = out_mask_logits[0]  # First object's mask
            mask_min = float(mask_logits.min())
            mask_max = float(mask_logits.max())
            mask_mean = float(mask_logits.mean())
            print(f"Debug: Mask logits - min: {mask_min:.3f}, max: {mask_max:.3f}, mean: {mask_mean:.3f}")

            # Check if negative areas have lower logits
            if negative_count > 0:
                print("Debug: Negative clicks should result in lower mask logits in those areas")

        return out_obj_ids, out_mask_logits

    def add_box_annotation(self, frame_idx, box, obj_id=1):
        """
        Add bounding box annotation to a frame.

        Args:
            frame_idx (int): Frame index (0-based)
            box (list): Bounding box as [x1, y1, x2, y2]
            obj_id (int): Object ID for tracking

        Returns:
            tuple: (out_obj_ids, out_mask_logits) from the predictor
        """
        if self.inference_state is None:
            raise RuntimeError("Video state not initialized. Call init_video_state() first.")

        box = np.array(box, dtype=np.float32)

        print(f"Adding box annotation to frame {frame_idx}")
        print(f"Box: {box}")
        print(f"Object ID: {obj_id}")

        # Add box to the predictor
        _, out_obj_ids, out_mask_logits = self.predictor.add_new_points_or_box(
            inference_state=self.inference_state,
            frame_idx=frame_idx,
            obj_id=obj_id,
            box=box,
        )

        return out_obj_ids, out_mask_logits

    def test_negative_click_effectiveness(self, frame_idx, positive_point, negative_point, obj_id=1):
        """
        Test the effectiveness of negative clicks by comparing masks with and without negative clicks.

        Args:
            frame_idx (int): Frame index to test
            positive_point (list): [x, y] coordinates for positive click
            negative_point (list): [x, y] coordinates for negative click
            obj_id (int): Object ID for tracking

        Returns:
            dict: Test results with mask comparisons
        """
        if self.inference_state is None:
            raise RuntimeError("Video state not initialized. Call init_video_state() first.")

        print(f"\n=== Testing Negative Click Effectiveness ===")
        print(f"Frame: {frame_idx}, Object ID: {obj_id}")
        print(f"Positive click: {positive_point}")
        print(f"Negative click: {negative_point}")

        # Test 1: Only positive click
        print("\n1. Testing with only positive click...")
        _, _, mask_logits_pos_only = self.predictor.add_new_points_or_box(
            inference_state=self.inference_state,
            frame_idx=frame_idx,
            obj_id=obj_id,
            points=np.array([positive_point], dtype=np.float32),
            labels=np.array([1], dtype=np.int32),
        )

        # Reset state for clean test
        self.reset_state()
        self.init_video_state(self.video_frames_dir)

        # Test 2: Positive + negative clicks
        print("\n2. Testing with positive + negative clicks...")
        _, _, mask_logits_with_neg = self.predictor.add_new_points_or_box(
            inference_state=self.inference_state,
            frame_idx=frame_idx,
            obj_id=obj_id,
            points=np.array([positive_point, negative_point], dtype=np.float32),
            labels=np.array([1, 0], dtype=np.int32),
        )

        # Compare masks
        if len(mask_logits_pos_only) > 0 and len(mask_logits_with_neg) > 0:
            mask_pos_only = mask_logits_pos_only[0]
            mask_with_neg = mask_logits_with_neg[0]

            # Convert to binary masks
            binary_pos_only = (mask_pos_only > 0.0).cpu().numpy()
            binary_with_neg = (mask_with_neg > 0.0).cpu().numpy()

            # Calculate differences
            total_pixels = binary_pos_only.size
            pixels_removed = np.sum(binary_pos_only & ~binary_with_neg)
            pixels_added = np.sum(~binary_pos_only & binary_with_neg)

            # Check negative click area specifically
            neg_x, neg_y = int(negative_point[0]), int(negative_point[1])
            h, w = binary_pos_only.shape

            # Sample area around negative click (5x5 window)
            window_size = 5
            y_start = max(0, neg_y - window_size//2)
            y_end = min(h, neg_y + window_size//2 + 1)
            x_start = max(0, neg_x - window_size//2)
            x_end = min(w, neg_x + window_size//2 + 1)

            neg_area_before = binary_pos_only[y_start:y_end, x_start:x_end]
            neg_area_after = binary_with_neg[y_start:y_end, x_start:x_end]
            neg_area_reduced = np.sum(neg_area_before) - np.sum(neg_area_after)

            results = {
                'total_pixels': total_pixels,
                'pixels_removed': pixels_removed,
                'pixels_added': pixels_added,
                'negative_area_reduced': neg_area_reduced,
                'negative_click_effective': neg_area_reduced > 0,
                'mask_pos_only': mask_pos_only,
                'mask_with_neg': mask_with_neg
            }

            print(f"\n=== Results ===")
            print(f"Total pixels: {total_pixels}")
            print(f"Pixels removed by negative click: {pixels_removed}")
            print(f"Pixels added by negative click: {pixels_added}")
            print(f"Negative click area reduced: {neg_area_reduced} pixels")
            print(f"Negative click effective: {results['negative_click_effective']}")

            if not results['negative_click_effective']:
                print("\n⚠️  WARNING: Negative click appears ineffective!")
                print("Possible causes:")
                print("1. Negative click too close to positive click")
                print("2. Model confidence too high in that area")
                print("3. Need multiple negative clicks")
                print("4. Try different negative click locations")

            return results
        else:
            print("❌ Error: Could not generate masks for comparison")
            return None

    def reset_state(self):
        """Reset the video state to allow new object IDs."""
        print("Resetting video state to allow new object IDs...")
        self.inference_state = None
        if hasattr(self, 'video_frames_dir') and self.video_frames_dir:
            try:
                self.init_video_state(self.video_frames_dir)
                print("Video state reset successfully.")
            except Exception as e:
                print(f"Warning: Failed to reinitialize video state: {e}")
                print("You will need to call init_video_state() manually with a valid video path.")
        else:
            print("Warning: No video frames directory stored. Call init_video_state() manually.")

    def propagate_masks(self):
        """
        Propagate masks throughout the video.

        Returns:
            dict: Dictionary mapping frame indices to (obj_ids, mask_logits)
        """
        if self.inference_state is None:
            raise RuntimeError("Video state not initialized. Call init_video_state() first.")

        print("Propagating masks throughout the video...")

        # Propagate masks to all frames
        video_segments = {}
        for out_frame_idx, out_obj_ids, out_mask_logits in self.predictor.propagate_in_video(self.inference_state):
            video_segments[out_frame_idx] = {
                'obj_ids': out_obj_ids,
                'mask_logits': out_mask_logits
            }

        print(f"Propagation completed for {len(video_segments)} frames")
        print("Note: After propagation, no new object IDs can be added. Use 'reset_state' to restart.")
        return video_segments

    def visualize_frame_with_masks(self, frame_path, masks_data, output_path=None, show_plot=True):
        """
        Visualize a frame with overlaid masks.

        Args:
            frame_path (str): Path to the frame image
            masks_data (dict): Dictionary containing 'obj_ids' and 'mask_logits'
            output_path (str): Path to save the visualization (optional)
            show_plot (bool): Whether to display the plot

        Returns:
            matplotlib.figure.Figure: The created figure
        """
        # Load the frame
        frame = Image.open(frame_path)
        frame_array = np.array(frame)

        # Create figure
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.imshow(frame_array)
        ax.set_title(f"Frame: {Path(frame_path).name}")

        # Overlay masks
        if masks_data and 'mask_logits' in masks_data:
            obj_ids = masks_data['obj_ids']
            mask_logits = masks_data['mask_logits']

            for i, obj_id in enumerate(obj_ids):
                mask = (mask_logits[i] > 0.0).cpu().numpy()
                self._show_mask(mask, ax, obj_id=obj_id)

        ax.axis('off')

        # Save if output path provided
        if output_path:
            plt.savefig(output_path, bbox_inches='tight', dpi=150)
            print(f"Visualization saved to: {output_path}")

        # Show plot if requested
        if show_plot:
            plt.show()

        return fig

    def _show_mask(self, mask, ax, obj_id=None, random_color=False):
        """
        Display mask overlay on the given axes.

        Args:
            mask (np.ndarray): Binary mask array
            ax (matplotlib.axes.Axes): Matplotlib axes to draw on
            obj_id (int): Object ID for color selection
            random_color (bool): Whether to use random colors
        """
        if random_color:
            color = np.concatenate([np.random.random(3), np.array([0.6])], axis=0)
        else:
            # Use a more diverse color palette and ensure unique colors per object ID
            colors = [
                [1.0, 0.0, 0.0],  # Red
                [0.0, 1.0, 0.0],  # Green
                [0.0, 0.0, 1.0],  # Blue
                [1.0, 1.0, 0.0],  # Yellow
                [1.0, 0.0, 1.0],  # Magenta
                [0.0, 1.0, 1.0],  # Cyan
                [1.0, 0.5, 0.0],  # Orange
                [0.5, 0.0, 1.0],  # Purple
                [0.0, 0.5, 0.0],  # Dark Green
                [0.5, 0.5, 0.5],  # Gray
            ]

            if obj_id is None:
                color_rgb = colors[0]
            else:
                # Use modulo to cycle through colors, but ensure different objects get different colors
                color_idx = (obj_id - 1) % len(colors)
                color_rgb = colors[color_idx]

            color = np.array([*color_rgb, 0.6])  # Add alpha

        h, w = mask.shape[-2:]
        mask_image = mask.reshape(h, w, 1) * color.reshape(1, 1, -1)
        ax.imshow(mask_image)

    def _show_points(self, coords, labels, ax, marker_size=200):
        """
        Display point annotations on the given axes.

        Args:
            coords (np.ndarray): Point coordinates
            labels (np.ndarray): Point labels (1 for positive, 0 for negative)
            ax (matplotlib.axes.Axes): Matplotlib axes to draw on
            marker_size (int): Size of the markers
        """
        pos_points = coords[labels == 1]
        neg_points = coords[labels == 0]
        ax.scatter(pos_points[:, 0], pos_points[:, 1], color='green', marker='*',
                  s=marker_size, edgecolor='white', linewidth=1.25)
        ax.scatter(neg_points[:, 0], neg_points[:, 1], color='red', marker='*',
                  s=marker_size, edgecolor='white', linewidth=1.25)

    def _show_box(self, box, ax):
        """
        Display bounding box on the given axes.

        Args:
            box (np.ndarray): Bounding box as [x1, y1, x2, y2]
            ax (matplotlib.axes.Axes): Matplotlib axes to draw on
        """
        x0, y0 = box[0], box[1]
        w, h = box[2] - box[0], box[3] - box[1]
        ax.add_patch(plt.Rectangle((x0, y0), w, h, edgecolor='green',
                                 facecolor=(0, 0, 0, 0), lw=2))

    def export_masks(self, video_segments, output_dir):
        """
        Export masks as PNG files.

        Args:
            video_segments (dict): Video segments from propagate_masks()
            output_dir (str): Directory to save mask files
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        print(f"Exporting masks to: {output_dir}")

        for frame_idx, masks_data in tqdm(video_segments.items(), desc="Exporting masks"):
            if 'mask_logits' in masks_data:
                obj_ids = masks_data['obj_ids']
                mask_logits = masks_data['mask_logits']

                for i, obj_id in enumerate(obj_ids):
                    mask = (mask_logits[i] > 0.0).cpu().numpy().squeeze()

                    # Convert to uint8
                    mask_uint8 = (mask * 255).astype(np.uint8)

                    # Save mask
                    mask_filename = f"frame_{frame_idx:05d}_obj_{obj_id:02d}_mask.png"
                    mask_path = output_dir / mask_filename

                    Image.fromarray(mask_uint8).save(mask_path)

        print(f"Masks exported successfully!")

    def export_masks_with_overlay(self, video_segments, frames_dir, output_dir):
        """
        Export masks with overlay on original frames and create video.

        Args:
            video_segments (dict): Video segments from propagate_masks()
            frames_dir (str): Directory containing original frames
            output_dir (str): Directory to save overlaid frames
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        print(f"Exporting masks with overlay to: {output_dir}")

        # Get frame files
        frames_dir = Path(frames_dir)
        frame_files = sorted([f for f in frames_dir.glob("*") if f.suffix.lower() in ['.jpg', '.jpeg', '.png']])

        for frame_idx, masks_data in tqdm(video_segments.items(), desc="Creating overlay frames"):
            if frame_idx < len(frame_files):
                frame_path = frame_files[frame_idx]
                output_path = output_dir / f"overlay_{frame_path.name}"

                # Create visualization without showing
                self.visualize_frame_with_masks(
                    str(frame_path),
                    masks_data,
                    output_path=str(output_path),
                    show_plot=False
                )
                plt.close()  # Close figure to save memory

        print(f"Overlay frames exported successfully!")
        return str(output_dir)

    def clean_output_directory(self, output_dir):
        """
        Clean the output directory to prevent mixing results from different runs.

        Args:
            output_dir (str): Directory to clean
        """
        output_path = Path(output_dir)

        if output_path.exists():
            print(f"Cleaning existing output directory: {output_path}")

            # Remove masks directory
            masks_dir = output_path / "masks"
            if masks_dir.exists():
                shutil.rmtree(masks_dir)
                print(f"  - Removed masks directory")

            # Remove visualizations directory
            viz_dir = output_path / "visualizations"
            if viz_dir.exists():
                shutil.rmtree(viz_dir)
                print(f"  - Removed visualizations directory")

            # Remove any other files in the output directory
            for item in output_path.iterdir():
                if item.is_file():
                    item.unlink()
                    print(f"  - Removed file: {item.name}")
                elif item.is_dir():
                    shutil.rmtree(item)
                    print(f"  - Removed directory: {item.name}")

        print("Output directory cleaned successfully!")

    def display_frame_list(self, frame_files):
        """
        Display a list of available frames for user selection.

        Args:
            frame_files (list): List of frame file paths

        Returns:
            None
        """
        print("\n" + "=" * 60)
        print("Available Frames for Annotation")
        print("=" * 60)

        # Display frames in groups of 10 for better readability
        for i in range(0, len(frame_files), 10):
            end_idx = min(i + 10, len(frame_files))
            frame_group = frame_files[i:end_idx]

            print(f"\nFrames {i}-{end_idx-1}:")
            for j, frame_file in enumerate(frame_group):
                frame_idx = i + j
                frame_name = frame_file.name
                print(f"  [{frame_idx:3d}] {frame_name}")

        print(f"\nTotal frames available: {len(frame_files)}")
        print("=" * 60)

    def validate_coordinates(self, coords, frame_width, frame_height):
        """
        Validate that coordinates are within frame boundaries.

        Args:
            coords (list): List of coordinates to validate
            frame_width (int): Frame width
            frame_height (int): Frame height

        Returns:
            bool: True if all coordinates are valid
        """
        for coord in coords:
            x, y = coord[:2]  # Take first two elements (x, y)
            if x < 0 or x >= frame_width or y < 0 or y >= frame_height:
                print(f"Error: Coordinate ({x}, {y}) is outside frame boundaries")
                print(f"Frame size: {frame_width} x {frame_height}")
                return False
        return True

    def display_frame_with_annotations(self, frame_path, points=None, labels=None, boxes=None):
        """
        Display a frame with current annotations overlaid.

        Args:
            frame_path (str): Path to the frame image
            points (list): List of point coordinates
            labels (list): List of point labels
            boxes (list): List of bounding boxes

        Returns:
            tuple: (frame_width, frame_height)
        """
        # Load and display the frame
        frame = Image.open(frame_path)
        frame_array = np.array(frame)
        frame_width, frame_height = frame.size

        plt.figure(figsize=(12, 8))
        plt.imshow(frame_array)
        plt.title(f"Frame: {Path(frame_path).name} (Size: {frame_width}x{frame_height})")

        # Overlay existing annotations
        if points and labels:
            points_array = np.array(points)
            labels_array = np.array(labels)
            self._show_points(points_array, labels_array, plt.gca())

        if boxes:
            for box in boxes:
                self._show_box(np.array(box), plt.gca())

        plt.axis('off')
        plt.tight_layout()
        plt.show()

        return frame_width, frame_height


class VisualAnnotationInterface:
    """Interactive visual annotation interface for SAM2 video frames."""

    def __init__(self):
        self.fig = None
        self.ax = None
        self.frame_image = None
        self.frame_width = 0
        self.frame_height = 0

        # Annotation storage
        self.points = []
        self.labels = []
        self.boxes = []
        self.obj_ids = []

        # Visual elements
        self.point_markers = []
        self.box_patches = []

        # Interaction state
        self.annotation_mode = 'click'  # 'click' or 'box'
        self.box_start = None
        self.current_box_patch = None
        self.is_drawing_box = False
        self.current_object_id = 1  # Default object ID

        # UI elements
        self.buttons = {}
        self.object_id_textbox = None

    def setup_interface(self, frame_path):
        """
        Setup the visual annotation interface for a frame.

        Args:
            frame_path (str): Path to the frame image
        """
        # Load frame
        self.frame_image = Image.open(frame_path)
        frame_array = np.array(self.frame_image)
        self.frame_width, self.frame_height = self.frame_image.size

        # Create figure and axis with proper backend
        plt.close('all')  # Close any existing figures
        plt.ion()  # Turn on interactive mode

        self.fig, self.ax = plt.subplots(1, 1, figsize=(14, 10))
        self.fig.suptitle(f"Visual Annotation Interface - {Path(frame_path).name}", fontsize=14)

        # Display frame with proper extent to match image coordinates
        self.ax.imshow(frame_array, extent=[0, self.frame_width, self.frame_height, 0])
        self.ax.set_title(f"Frame Size: {self.frame_width}x{self.frame_height} | Mode: {self.annotation_mode}")
        self.ax.set_xlabel("X coordinate")
        self.ax.set_ylabel("Y coordinate")

        # Set axis limits to match image coordinates
        self.ax.set_xlim(0, self.frame_width)
        self.ax.set_ylim(self.frame_height, 0)  # Invert Y axis to match image coordinates

        # Setup event handlers with proper connection
        self.cid_press = self.fig.canvas.mpl_connect('button_press_event', self.on_mouse_press)
        self.cid_release = self.fig.canvas.mpl_connect('button_release_event', self.on_mouse_release)
        self.cid_motion = self.fig.canvas.mpl_connect('motion_notify_event', self.on_mouse_move)

        # Setup UI buttons
        self.setup_buttons()

        # Clear previous annotations
        self.clear_visual_annotations()

        # Set initial mode
        self.set_annotation_mode('click')

        plt.tight_layout()
        plt.show(block=False)
        plt.draw()  # Force initial draw

    def setup_buttons(self):
        """Setup UI buttons for annotation control."""
        # Button positions (left, bottom, width, height)
        button_height = 0.04
        button_width = 0.12
        button_spacing = 0.02
        start_x = 0.02
        start_y = 0.02

        # Click mode button
        ax_click = plt.axes([start_x, start_y, button_width, button_height])
        self.buttons['click'] = Button(ax_click, 'Click Mode')
        self.buttons['click'].on_clicked(lambda x: self.set_annotation_mode('click'))

        # Box mode button
        ax_box = plt.axes([start_x + button_width + button_spacing, start_y, button_width, button_height])
        self.buttons['box'] = Button(ax_box, 'Box Mode')
        self.buttons['box'].on_clicked(lambda x: self.set_annotation_mode('box'))

        # Clear button
        ax_clear = plt.axes([start_x + 2*(button_width + button_spacing), start_y, button_width, button_height])
        self.buttons['clear'] = Button(ax_clear, 'Clear All')
        self.buttons['clear'].on_clicked(lambda x: self.clear_annotations())

        # Undo button
        ax_undo = plt.axes([start_x + 3*(button_width + button_spacing), start_y, button_width, button_height])
        self.buttons['undo'] = Button(ax_undo, 'Undo Last')
        self.buttons['undo'].on_clicked(lambda x: self.undo_last_annotation())

        # Apply button
        ax_apply = plt.axes([start_x + 4*(button_width + button_spacing), start_y, button_width, button_height])
        self.buttons['apply'] = Button(ax_apply, 'Apply & Close')
        self.buttons['apply'].on_clicked(lambda x: self.apply_annotations())

        # Cancel button
        ax_cancel = plt.axes([start_x + 5*(button_width + button_spacing), start_y, button_width, button_height])
        self.buttons['cancel'] = Button(ax_cancel, 'Cancel')
        self.buttons['cancel'].on_clicked(lambda x: self.cancel_annotations())

        # Object ID text box
        textbox_y = start_y + button_height + button_spacing
        ax_textbox = plt.axes([start_x, textbox_y, button_width * 1.5, button_height])
        self.object_id_textbox = TextBox(ax_textbox, 'Obj ID: ', initial=str(self.current_object_id))
        self.object_id_textbox.on_submit(self.update_object_id)

        # Update button colors based on current mode
        self.update_button_colors()

    def update_button_colors(self):
        """Update button colors based on current annotation mode."""
        if 'click' in self.buttons:
            self.buttons['click'].color = 'lightgreen' if self.annotation_mode == 'click' else 'lightgray'
        if 'box' in self.buttons:
            self.buttons['box'].color = 'lightgreen' if self.annotation_mode == 'box' else 'lightgray'

        if self.fig:
            self.fig.canvas.draw()

    def set_annotation_mode(self, mode):
        """Set the annotation mode (click or box)."""
        self.annotation_mode = mode
        self.update_button_colors()

        if mode == 'click':
            self.ax.set_title(f"Click Mode - Left: Positive, Right: Negative | Frame: {self.frame_width}x{self.frame_height}")
        else:
            self.ax.set_title(f"Box Mode - Click and drag to draw bounding box | Frame: {self.frame_width}x{self.frame_height}")

        self.fig.canvas.draw()

    def update_object_id(self, text):
        """Update the current object ID from text box input."""
        try:
            self.current_object_id = int(text)
            print(f"Object ID updated to: {self.current_object_id}")
        except ValueError:
            print(f"Invalid object ID: {text}. Using default: {self.current_object_id}")
            if self.object_id_textbox:
                self.object_id_textbox.set_val(str(self.current_object_id))

    def get_current_object_id(self):
        """Get the current object ID from the text box, with fallback to stored value."""
        if self.object_id_textbox:
            try:
                # Get the current text value directly from the text box
                current_text = self.object_id_textbox.text
                obj_id = int(current_text)
                # Update the stored value to keep it in sync
                self.current_object_id = obj_id
                return obj_id
            except (ValueError, AttributeError):
                # Fallback to stored value if text box value is invalid
                return self.current_object_id
        else:
            return self.current_object_id

    def on_mouse_press(self, event):
        """Handle mouse press events."""
        # Debug print
        print(f"Mouse press detected: button={event.button}, inaxes={event.inaxes == self.ax}")

        if event.inaxes != self.ax:
            print("Click outside axes, ignoring")
            return

        x, y = event.xdata, event.ydata
        print(f"Raw coordinates: x={x}, y={y}")

        if x is None or y is None:
            print("Invalid coordinates (None), ignoring")
            return

        # Round coordinates to integers for pixel precision
        x, y = round(x), round(y)
        print(f"Rounded coordinates: x={x}, y={y}")

        # Validate coordinates are within frame bounds
        if x < 0 or x >= self.frame_width or y < 0 or y >= self.frame_height:
            print(f"Coordinates out of bounds: ({x}, {y}) not in (0, 0) to ({self.frame_width}, {self.frame_height})")
            return

        print(f"Processing {self.annotation_mode} annotation at ({x}, {y})")

        if self.annotation_mode == 'click':
            self.handle_click_annotation(x, y, event.button)
        elif self.annotation_mode == 'box':
            self.handle_box_start(x, y, event.button)

    def on_mouse_release(self, event):
        """Handle mouse release events."""
        print(f"Mouse release detected: button={event.button}, mode={self.annotation_mode}")

        if event.inaxes != self.ax or self.annotation_mode != 'box':
            return

        x, y = event.xdata, event.ydata
        if x is None or y is None:
            return

        # Round coordinates to integers
        x, y = round(x), round(y)
        print(f"Box end coordinates: ({x}, {y})")

        self.handle_box_end(x, y, event.button)

    def on_mouse_move(self, event):
        """Handle mouse move events for box drawing."""
        if (event.inaxes != self.ax or self.annotation_mode != 'box' or
            not self.is_drawing_box or self.box_start is None):
            return

        x, y = event.xdata, event.ydata
        if x is None or y is None:
            return

        # Round coordinates to integers
        x, y = round(x), round(y)
        self.update_box_preview(x, y)

    def handle_click_annotation(self, x, y, button):
        """Handle click annotation (positive/negative points)."""
        print(f"Handling click annotation: button={button} at ({x}, {y})")

        # Left click = positive (1), Right click = negative (0)
        label = 1 if button == 1 else 0
        label_text = "positive" if label == 1 else "negative"

        # Get current object ID directly from text box (fixes lag issue)
        obj_id = self.get_current_object_id()
        print(f"Using object ID: {obj_id}")

        # Store annotation
        self.points.append([x, y])
        self.labels.append(label)
        self.obj_ids.append(obj_id)

        # Add visual marker with proper styling
        color = 'green' if label == 1 else 'red'
        marker = '*' if label == 1 else 'X'

        print(f"Creating visual marker: color={color}, marker={marker}")

        # Create the scatter plot marker
        point_marker = self.ax.scatter(x, y, c=color, marker=marker, s=300,
                                     edgecolors='white', linewidth=3, zorder=10,
                                     alpha=0.8)
        self.point_markers.append(point_marker)

        # Add text label for object ID
        text_label = self.ax.text(x + 10, y - 10, f"obj{obj_id}",
                                fontsize=10, color=color, weight='bold',
                                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.7),
                                zorder=11)
        self.point_markers.append(text_label)  # Store text with markers for cleanup

        # Update display
        print(f"Added {label_text} click at ({x}, {y}) for object {obj_id}")

        # Force redraw
        self.fig.canvas.draw_idle()
        self.fig.canvas.flush_events()

        print(f"Total annotations: {len(self.points)} points, {len(self.boxes)} boxes")

    def handle_box_start(self, x, y, button):
        """Handle start of bounding box drawing."""
        print(f"Starting box at ({x}, {y}) with button {button}")

        if button != 1:  # Only left click for boxes
            print("Ignoring non-left click for box mode")
            return

        self.box_start = (x, y)
        self.is_drawing_box = True

        print(f"Box start set to: {self.box_start}")

        # Create preview box patch
        self.current_box_patch = Rectangle((x, y), 0, 0, linewidth=3,
                                         edgecolor='blue', facecolor='none',
                                         linestyle='--', zorder=5, alpha=0.7)
        self.ax.add_patch(self.current_box_patch)

        print("Preview box patch created")

        # Force redraw
        self.fig.canvas.draw_idle()
        self.fig.canvas.flush_events()

    def handle_box_end(self, x, y, button):
        """Handle end of bounding box drawing."""
        print(f"Ending box at ({x}, {y}) with button {button}")

        if not self.is_drawing_box or self.box_start is None or button != 1:
            print("Invalid box end conditions")
            return

        x1, y1 = self.box_start
        x2, y2 = x, y

        print(f"Box coordinates: ({x1}, {y1}) to ({x2}, {y2})")

        # Ensure proper box format (x1 < x2, y1 < y2)
        if x1 > x2:
            x1, x2 = x2, x1
        if y1 > y2:
            y1, y2 = y2, y1

        print(f"Normalized box coordinates: ({x1}, {y1}) to ({x2}, {y2})")

        # Validate box size
        width, height = abs(x2 - x1), abs(y2 - y1)
        if width < 5 or height < 5:
            print(f"Box too small ({width}x{height}). Please draw a larger bounding box.")
            self.cancel_current_box()
            return

        print(f"Box size valid: {width}x{height}")

        # Get current object ID directly from text box (fixes lag issue)
        obj_id = self.get_current_object_id()
        print(f"Using object ID: {obj_id}")

        # Store annotation
        box = [x1, y1, x2, y2]
        self.boxes.append(box)
        self.obj_ids.append(obj_id)

        # Replace preview with final box
        if self.current_box_patch:
            self.current_box_patch.remove()
            print("Removed preview box")

        final_box_patch = Rectangle((x1, y1), x2-x1, y2-y1, linewidth=3,
                                  edgecolor='green', facecolor='none', zorder=5,
                                  alpha=0.8)
        self.ax.add_patch(final_box_patch)
        self.box_patches.append(final_box_patch)

        # Add text label for object ID
        text_x, text_y = x1 + 5, y1 - 5
        text_label = self.ax.text(text_x, text_y, f"box{obj_id}",
                                fontsize=10, color='green', weight='bold',
                                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.7),
                                zorder=11)
        self.box_patches.append(text_label)  # Store text with patches for cleanup

        # Reset state
        self.box_start = None
        self.is_drawing_box = False
        self.current_box_patch = None

        print(f"Added bounding box ({x1}, {y1}) to ({x2}, {y2}) for object {obj_id}")

        # Force redraw
        self.fig.canvas.draw_idle()
        self.fig.canvas.flush_events()

        print(f"Total annotations: {len(self.points)} points, {len(self.boxes)} boxes")

    def update_box_preview(self, x, y):
        """Update the preview of the bounding box being drawn."""
        if self.current_box_patch and self.box_start:
            x1, y1 = self.box_start

            # Calculate proper rectangle coordinates
            min_x = min(x1, x)
            min_y = min(y1, y)
            width = abs(x - x1)
            height = abs(y - y1)

            # Update the preview rectangle
            self.current_box_patch.set_xy((min_x, min_y))
            self.current_box_patch.set_width(width)
            self.current_box_patch.set_height(height)

            # Force redraw for smooth preview
            self.fig.canvas.draw_idle()

    def cancel_current_box(self):
        """Cancel the current box being drawn."""
        if self.current_box_patch:
            self.current_box_patch.remove()
            self.current_box_patch = None
        self.box_start = None
        self.is_drawing_box = False
        self.fig.canvas.draw()

    def clear_annotations(self):
        """Clear all annotations for the current frame."""
        # Clear data
        self.points.clear()
        self.labels.clear()
        self.boxes.clear()
        self.obj_ids.clear()

        # Clear visual elements
        self.clear_visual_annotations()
        print("All annotations cleared.")

    def clear_visual_annotations(self):
        """Clear visual annotation elements from the plot."""
        print("Clearing visual annotations...")

        # Remove point markers
        for marker in self.point_markers:
            try:
                marker.remove()
            except Exception as e:
                print(f"Error removing point marker: {e}")
        self.point_markers.clear()

        # Remove box patches
        for patch in self.box_patches:
            try:
                patch.remove()
            except Exception as e:
                print(f"Error removing box patch: {e}")
        self.box_patches.clear()

        # Cancel any current box
        self.cancel_current_box()

        if self.fig:
            self.fig.canvas.draw_idle()
            self.fig.canvas.flush_events()

        print("Visual annotations cleared")

    def undo_last_annotation(self):
        """Undo the last annotation."""
        if self.points:
            # Remove last point
            self.points.pop()
            self.labels.pop()
            self.obj_ids.pop()

            # Remove visual marker
            if self.point_markers:
                marker = self.point_markers.pop()
                marker.remove()

            print("Undid last click annotation.")

        elif self.boxes:
            # Remove last box
            self.boxes.pop()
            self.obj_ids.pop()

            # Remove visual patch
            if self.box_patches:
                patch = self.box_patches.pop()
                patch.remove()

            print("Undid last bounding box annotation.")

        else:
            print("No annotations to undo.")

        if self.fig:
            self.fig.canvas.draw()

    def apply_annotations(self):
        """Apply annotations and close the interface."""
        print(f"Applying annotations: {len(self.points)} points, {len(self.boxes)} boxes")
        self.annotation_complete = True
        plt.close(self.fig)

    def cancel_annotations(self):
        """Cancel annotations and close the interface."""
        print("Annotations cancelled by user")
        self.clear_annotations()
        self.annotation_complete = False
        plt.close(self.fig)

    def get_annotations(self):
        """Get the current annotations."""
        return {
            'points': self.points.copy(),
            'labels': self.labels.copy(),
            'boxes': self.boxes.copy(),
            'obj_ids': self.obj_ids.copy()
        }

    def wait_for_completion(self):
        """Wait for user to complete annotations."""
        self.annotation_complete = None

        # Show instructions
        print("\n" + "=" * 60)
        print("Visual Annotation Interface")
        print("=" * 60)
        print("Instructions:")
        print("• Click Mode: Left click = positive point, Right click = negative point")
        print("• Box Mode: Click and drag to draw bounding boxes")
        print("• Use buttons to switch modes, clear, undo, apply, or cancel")
        print("• Close the window or click 'Apply & Close' when finished")
        print("=" * 60)
        print("Interface is ready for interaction...")

        # Ensure the figure is properly displayed
        if self.fig:
            self.fig.show()
            self.fig.canvas.draw()
            self.fig.canvas.flush_events()

        # Keep the interface open until user completes or cancels
        while self.annotation_complete is None:
            try:
                plt.pause(0.1)

                # Check if figure still exists
                if not plt.get_fignums():  # Figure was closed
                    print("Figure was closed, saving annotations")
                    # If user has annotations and didn't explicitly cancel, save them
                    if self.points or self.boxes:
                        print(f"Saving {len(self.points)} points and {len(self.boxes)} boxes")
                        self.annotation_complete = True
                    else:
                        print("No annotations to save")
                        self.annotation_complete = False
                    break

            except KeyboardInterrupt:
                print("\nInterrupted by user")
                self.annotation_complete = False
                break
            except Exception as e:
                print(f"Error in annotation interface: {e}")
                self.annotation_complete = False
                break

        return self.annotation_complete

    def validate_coordinates(self, coords, frame_width, frame_height):
        """
        Validate that coordinates are within frame boundaries.

        Args:
            coords (list): List of coordinates to validate
            frame_width (int): Frame width
            frame_height (int): Frame height

        Returns:
            bool: True if all coordinates are valid
        """
        for coord in coords:
            x, y = coord[:2]  # Take first two elements (x, y)
            if x < 0 or x >= frame_width or y < 0 or y >= frame_height:
                print(f"Error: Coordinate ({x}, {y}) is outside frame boundaries")
                print(f"Frame size: {frame_width} x {frame_height}")
                return False
        return True


def run_interactive_demo(video_path, model_size="base_plus", max_frames=100, output_dir=None, input_size=None):
    """
    Run interactive demo with visual annotation interface.

    Args:
        video_path (str): Path to the input video file
        model_size (str): SAM2 model size
        max_frames (int): Maximum number of frames to extract
        output_dir (str): Output directory for results
        input_size (int): Input size for the model
    """
    print("=" * 80)
    print("SAM2 Video Predictor v3 - Interactive Demo")
    print("=" * 80)

    try:
        # Initialize directory manager
        video_name = Path(video_path).stem
        if not output_dir:
            output_dir = f"./sam2_output"

        dir_manager = DirectoryManager(output_dir)
        dir_manager.setup_directories(video_name)

        # Initialize SAM2 predictor
        predictor = SAM2VideoPredictor(
            model_size=model_size,
            input_size=input_size
        )

        # Preprocess video to frames
        print(f"\nPreprocessing video...")
        frames_dir = predictor.preprocess_video(
            video_path,
            max_frames=max_frames,
            output_dir=str(dir_manager.base_output_dir)
        )
        dir_manager.set_video_dir(frames_dir)

        # Initialize video state
        print(f"\nInitializing video state...")
        predictor.init_video_state(frames_dir)

        # Get frame files
        frames_path = Path(frames_dir)
        frame_files = sorted([f for f in frames_path.glob("*") if f.suffix.lower() in ['.jpg', '.jpeg', '.png']])

        if not frame_files:
            print("❌ No frame files found!")
            return

        print(f"Found {len(frame_files)} frames for annotation")

        # Display frame list
        predictor.display_frame_list(frame_files)

        # Interactive annotation loop
        frame_annotations = {}
        current_frame_idx = 0

        while True:
            print(f"\n" + "=" * 60)
            print(f"Interactive Annotation - Frame {current_frame_idx}/{len(frame_files)-1}")
            print("=" * 60)
            print("Options:")
            print("1. Annotate current frame")
            print("2. Go to specific frame")
            print("3. Propagate masks and export")
            print("4. Exit")

            choice = input("Select option (1-4): ").strip()

            if choice == '1':
                # Annotate current frame
                frame_file = frame_files[current_frame_idx]
                print(f"\nAnnotating frame: {frame_file.name}")

                # Use visual annotation interface
                interface = VisualAnnotationInterface()
                interface.setup_interface(str(frame_file))

                # Wait for user to complete annotations
                completed = interface.wait_for_completion()

                if completed:
                    annotations = interface.get_annotations()
                    if annotations['points'] or annotations['boxes']:
                        frame_annotations[current_frame_idx] = annotations
                        print(f"✅ Saved annotations for frame {current_frame_idx}")

                        # Apply annotations to predictor
                        _apply_annotations_to_predictor(
                            predictor, current_frame_idx, annotations
                        )
                    else:
                        print("No annotations to save")
                else:
                    print("Annotation cancelled")

                # Move to next frame
                if current_frame_idx < len(frame_files) - 1:
                    current_frame_idx += 1

            elif choice == '2':
                # Go to specific frame
                try:
                    frame_idx = int(input(f"Enter frame index (0-{len(frame_files)-1}): "))
                    if 0 <= frame_idx < len(frame_files):
                        current_frame_idx = frame_idx
                        print(f"Moved to frame {current_frame_idx}")
                    else:
                        print("Invalid frame index")
                except ValueError:
                    print("Invalid input")

            elif choice == '3':
                # Propagate masks and export
                if frame_annotations:
                    print(f"\nPropagating masks for {len(frame_annotations)} annotated frames...")

                    try:
                        # Propagate masks
                        video_segments = predictor.propagate_masks()

                        # Export masks
                        masks_dir = dir_manager.get_paths()['mask_data_dir']
                        predictor.export_masks(video_segments, masks_dir)

                        # Export visualizations
                        result_dir = dir_manager.get_paths()['result_dir']
                        overlay_dir = predictor.export_masks_with_overlay(
                            video_segments, frames_dir, result_dir
                        )

                        # Create video if requested
                        output_video_path = str(dir_manager.base_output_dir / f"{video_name}_annotated.mp4")
                        create_video_from_images(overlay_dir, output_video_path, 25)

                        print(f"✅ Processing completed!")
                        print(f"Results saved to: {dir_manager.base_output_dir}")
                        print(f"Video created: {output_video_path}")

                    except Exception as e:
                        print(f"❌ Error during propagation: {e}")
                        import traceback
                        traceback.print_exc()
                else:
                    print("No annotations to propagate")

            elif choice == '4':
                print("Exiting interactive demo")
                break
            else:
                print("Invalid choice")

        print(f"\nInteractive demo completed!")

    except Exception as e:
        print(f"❌ Error in interactive demo: {e}")
        import traceback
        traceback.print_exc()


def _apply_annotations_to_predictor(predictor, frame_idx, annotations):
    """Apply annotations to the SAM2 predictor."""
    try:
        # Apply point annotations
        if annotations['points'] and annotations['labels']:
            points = annotations['points']
            labels = annotations['labels']
            obj_ids = annotations['obj_ids']

            # Group by object ID
            obj_points = {}
            obj_labels = {}

            for point, label, obj_id in zip(points, labels, obj_ids):
                if obj_id not in obj_points:
                    obj_points[obj_id] = []
                    obj_labels[obj_id] = []
                obj_points[obj_id].append(point)
                obj_labels[obj_id].append(label)

            # Add annotations for each object
            for obj_id in obj_points:
                predictor.add_click_annotation(
                    frame_idx,
                    obj_points[obj_id],
                    obj_labels[obj_id],
                    obj_id
                )

        # Apply box annotations
        if annotations['boxes']:
            boxes = annotations['boxes']
            obj_ids = annotations['obj_ids']

            # Get unique object IDs for boxes
            box_obj_ids = obj_ids[len(annotations['points']):]  # Box obj_ids come after point obj_ids

            for box, obj_id in zip(boxes, box_obj_ids):
                predictor.add_box_annotation(frame_idx, box, obj_id)

    except Exception as e:
        print(f"Error applying annotations: {e}")


def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="SAM2 Video Predictor v3 - Enhanced video segmentation with configurable models",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic usage with default model
  python video_predictor_sam2_v3.py --video input.mp4

  # Use specific model size
  python video_predictor_sam2_v3.py --video input.mp4 --model tiny

  # Specify input size and output directory
  python video_predictor_sam2_v3.py --video input.mp4 --model base_plus --input_size 1024 --output_dir ./results

  # Create video from processed frames
  python video_predictor_sam2_v3.py --video input.mp4 --create_video --frame_rate 30
        """
    )

    # Required arguments
    parser.add_argument(
        "--video",
        type=str,
        required=True,
        help="Path to input video file"
    )

    # Model configuration
    parser.add_argument(
        "--model",
        type=str,
        choices=["tiny", "small", "base_plus", "large"],
        default="base_plus",
        help="SAM2 model size to use (default: base_plus)"
    )

    parser.add_argument(
        "--input_size",
        type=int,
        help="Input size for the Video Segmentation model (optional)"
    )

    # Processing options
    parser.add_argument(
        "--max_frames",
        type=int,
        default=100,
        help="Maximum number of frames to extract from video (default: 100)"
    )

    parser.add_argument(
        "--output_dir",
        type=str,
        default="./sam2_output",
        help="Output directory for all results (default: ./sam2_output)"
    )

    # Video creation options
    parser.add_argument(
        "--create_video",
        action="store_true",
        help="Create video from processed frames"
    )

    parser.add_argument(
        "--frame_rate",
        type=int,
        default=25,
        help="Frame rate for output video (default: 25)"
    )

    # Device options
    parser.add_argument(
        "--device",
        type=str,
        choices=["cuda", "cpu", "mps"],
        help="Device to use for computation (auto-detect if not specified)"
    )

    # Interactive mode
    parser.add_argument(
        "--interactive",
        action="store_true",
        help="Enable interactive annotation mode"
    )

    return parser.parse_args()


def main():
    """Main function for the enhanced SAM2 video predictor."""
    args = parse_arguments()

    print("=" * 80)
    print("SAM2 Video Predictor v3 - Enhanced Edition")
    print("=" * 80)
    print(f"Video: {args.video}")
    print(f"Model: {args.model}")
    print(f"Input size: {args.input_size}")
    print(f"Max frames: {args.max_frames}")
    print(f"Output directory: {args.output_dir}")
    print(f"Device: {args.device}")
    print(f"Interactive mode: {args.interactive}")
    print("=" * 80)

    try:
        # Initialize directory manager
        video_name = Path(args.video).stem
        dir_manager = DirectoryManager(args.output_dir)
        dir_manager.setup_directories(video_name)

        # Initialize SAM2 predictor
        predictor = SAM2VideoPredictor(
            model_size=args.model,
            device=args.device,
            input_size=args.input_size
        )

        # Print model information
        model_info = predictor.get_model_info()
        print(f"\nModel Information:")
        for key, value in model_info.items():
            print(f"  {key}: {value}")

        # Preprocess video to frames
        print(f"\nPreprocessing video...")
        frames_dir = predictor.preprocess_video(
            args.video,
            max_frames=args.max_frames,
            output_dir=str(dir_manager.base_output_dir)
        )
        dir_manager.set_video_dir(frames_dir)

        # Initialize video state
        print(f"\nInitializing video state...")
        predictor.init_video_state(frames_dir)

        if args.interactive:
            print(f"\nStarting interactive annotation mode...")
            # Run the interactive demo
            run_interactive_demo(
                video_path=args.video,
                model_size=args.model,
                max_frames=args.max_frames,
                output_dir=args.output_dir,
                input_size=args.input_size
            )
            return 0  # Exit after interactive demo
        else:
            print(f"\nNon-interactive mode - basic processing completed.")
            print("For interactive annotation, use --interactive flag.")

        # Test video creation functionality if requested
        if args.create_video:
            print(f"\nTesting video creation functionality...")
            frames_dir = dir_manager.get_paths()['video_dir']
            if frames_dir and os.path.exists(frames_dir):
                output_video_path = str(dir_manager.base_output_dir / f"{video_name}_test.mp4")
                try:
                    create_video_from_images(frames_dir, output_video_path, args.frame_rate)
                    print(f"✅ Video creation test successful: {output_video_path}")
                except Exception as e:
                    print(f"❌ Video creation test failed: {e}")
            else:
                print("❌ No frames directory found for video creation test")

        # Get directory paths for reference
        paths = dir_manager.get_paths()
        print(f"\nDirectory structure created:")
        for key, path in paths.items():
            if path:
                print(f"  {key}: {path}")

        print(f"\nProcessing completed successfully!")
        print(f"Use the interactive mode (--interactive) to add annotations and generate masks.")

    except Exception as e:
        print(f"\nError during processing: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
