#!/usr/bin/env python3
"""
SAM2 Video Predictor Standalone Script v3

Enhanced version with multi-checkpoint support, adjustable input size,
video creation functionality, and directory structure management.

Features:
- Automatic video-to-frame conversion using video2frame.py
- Multiple SAM2 model size support (tiny, small, base_plus, large)
- Configurable model selection via command-line arguments
- Adjustable input size for Video Segmentation model
- Video creation functionality from processed image sequences
- Directory structure management (video_dir, mask_data_dir, json_data_dir, result_dir)
- Interactive click-based segmentation
- Bounding box support
- Multi-object tracking
- Frame-by-frame processing
- Mask visualization and export

Requirements:
- SAM2 environment with all dependencies installed
- CUDA-capable GPU (recommended)
- Proper PYTHONPATH configuration

Usage:
    PYTHONPATH="/c/Users/<USER>/Codings/sam2davis/sam2:/c/Users/<USER>/Codings/sam2davis:$PYTHONPATH" \
    "/c/Users/<USER>/.conda/envs/sam2_env_py310/python.exe" \
    video_predictor_sam2_v3.py --video path/to/video.mp4 --model base_plus --input_size 1024

Author: Enhanced from Meta's SAM2 video_predictor_example.ipynb
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path
import numpy as np
import torch
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
from matplotlib.widgets import Button, TextBox
from PIL import Image
import cv2
from tqdm import tqdm
import warnings
import shutil
import glob

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")

# Environment setup for Apple MPS fallback
os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"

# Add current directory to Python path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Add Grounded-SAM-2 directory to path if it exists
grounded_sam2_dir = os.path.join(current_dir, "Grounded-SAM-2")
if os.path.exists(grounded_sam2_dir) and grounded_sam2_dir not in sys.path:
    sys.path.insert(0, grounded_sam2_dir)

# SAM2 imports
try:
    from sam2.build_sam import build_sam2_video_predictor
except ImportError as e:
    print(f"SAM2 import error: {e}")
    print("Please ensure you're running from the correct directory with proper PYTHONPATH")
    print("Expected PYTHONPATH format:")
    print('PYTHONPATH="/path/to/sam2davis/sam2:/path/to/sam2davis:$PYTHONPATH"')
    print(f"Current directory: {current_dir}")
    print(f"Grounded-SAM-2 directory: {grounded_sam2_dir}")
    print(f"Python path: {sys.path}")
    sys.exit(1)


class SAM2VideoPredictor:
    """Standalone SAM2 Video Predictor class."""
    
    def __init__(self, model_size="base_plus", device=None):
        """
        Initialize the SAM2 Video Predictor.
        
        Args:
            model_size (str): Model size - tiny, small, base_plus, or large
            device (str): Device to use - cuda, cpu, or mps
        """
        self.model_size = model_size
        self.device = self._setup_device(device)
        self.predictor = None
        self.inference_state = None
        
        print(f"Initializing SAM2 Video Predictor...")
        print(f"Model size: {model_size}")
        print(f"Device: {self.device}")
        
        # Load the model
        self._load_model()
    
    def _setup_device(self, device=None):
        """Setup computation device with proper configuration."""
        if device is None:
            if torch.cuda.is_available():
                device = torch.device("cuda")
            elif torch.backends.mps.is_available():
                device = torch.device("mps")
            else:
                device = torch.device("cpu")
        else:
            device = torch.device(device)
        
        print(f"Using device: {device}")
        
        # Configure device-specific optimizations
        if device.type == "cuda":
            # Use bfloat16 for CUDA
            torch.autocast("cuda", dtype=torch.bfloat16).__enter__()
            # Enable TF32 for Ampere GPUs
            if torch.cuda.get_device_properties(0).major >= 8:
                torch.backends.cuda.matmul.allow_tf32 = True
                torch.backends.cudnn.allow_tf32 = True
                print("Enabled TF32 for Ampere GPU")
        elif device.type == "mps":
            print("Warning: MPS support is preliminary. SAM2 is trained with CUDA and might")
            print("give numerically different outputs and sometimes degraded performance on MPS.")
        
        return device
    
    def _find_config_and_checkpoint_paths(self):
        """Find the correct paths for SAM2 configs and checkpoints."""
        # Possible config base paths
        possible_config_bases = [
            "configs/sam2.1",  # Current directory
            "sam2/configs/sam2.1",  # sam2 subdirectory
            "../configs/sam2.1",  # Parent directory
            "Grounded-SAM-2/configs/sam2.1",  # Grounded-SAM-2 subdirectory
        ]
        
        # Possible checkpoint base paths  
        possible_checkpoint_bases = [
            "checkpoints",  # Current directory
            "../checkpoints",  # Parent directory
            "Grounded-SAM-2/checkpoints",  # Grounded-SAM-2 subdirectory
        ]
        
        # Find working config path
        config_base = None
        for path in possible_config_bases:
            if os.path.exists(path):
                config_base = path
                break
        
        # Find working checkpoint path
        checkpoint_base = None
        for path in possible_checkpoint_bases:
            if os.path.exists(path):
                checkpoint_base = path
                break
        
        if not config_base:
            raise RuntimeError(f"Could not find SAM2 configs. Searched: {possible_config_bases}")
        if not checkpoint_base:
            raise RuntimeError(f"Could not find SAM2 checkpoints. Searched: {possible_checkpoint_bases}")
        
        print(f"Found config base: {config_base}")
        print(f"Found checkpoint base: {checkpoint_base}")
        
        return config_base, checkpoint_base
    
    def _load_model(self):
        """Load the SAM2 video predictor model."""
        config_base, checkpoint_base = self._find_config_and_checkpoint_paths()
        
        # Model configurations matching the working script format
        model_configs = {
            "tiny": (f"{config_base}/sam2.1_hiera_t.yaml", f"./{checkpoint_base}/sam2.1_hiera_tiny.pt"),
            "small": (f"{config_base}/sam2.1_hiera_s.yaml", f"./{checkpoint_base}/sam2.1_hiera_small.pt"),
            "base_plus": (f"{config_base}/sam2.1_hiera_b+.yaml", f"./{checkpoint_base}/sam2.1_hiera_base_plus.pt"),
            "large": (f"{config_base}/sam2.1_hiera_l.yaml", f"./{checkpoint_base}/sam2.1_hiera_large.pt")
        }
        
        if self.model_size not in model_configs:
            raise ValueError(f"Invalid model size: {self.model_size}. Choose from: {list(model_configs.keys())}")
        
        model_cfg, checkpoint_path = model_configs[self.model_size]
        
        # Verify files exist
        if not os.path.exists(model_cfg):
            raise FileNotFoundError(f"Config file not found: {model_cfg}")
        if not os.path.exists(checkpoint_path):
            raise FileNotFoundError(f"Checkpoint file not found: {checkpoint_path}")
        
        print(f"Loading model config: {model_cfg}")
        print(f"Loading checkpoint: {checkpoint_path}")
        
        # Build the video predictor
        self.predictor = build_sam2_video_predictor(model_cfg, checkpoint_path, device=self.device)
        print("SAM2 video predictor loaded successfully!")
    
    def preprocess_video(self, video_path, max_frames=100, output_dir=None):
        """
        Convert video to frames using the video2frame.py script.
        
        Args:
            video_path (str): Path to the input video file
            max_frames (int): Maximum number of frames to extract
            output_dir (str): Output directory for frames (optional)
            
        Returns:
            str: Path to the directory containing extracted frames
        """
        video_path = Path(video_path)
        
        if not video_path.exists():
            raise FileNotFoundError(f"Video file not found: {video_path}")
        
        print(f"Preprocessing video: {video_path}")
        
        # Find video2frame.py script
        video2frame_script = None
        possible_paths = [
            "video2frame.py",  # Current directory
            "../video2frame.py",  # Parent directory
            os.path.join(current_dir, "video2frame.py"),  # Script directory
            os.path.join(os.path.dirname(current_dir), "video2frame.py"),  # Parent of script directory
        ]

        for path in possible_paths:
            if os.path.exists(path):
                video2frame_script = path
                break

        if not video2frame_script:
            raise FileNotFoundError(f"video2frame.py script not found. Searched: {possible_paths}")

        # Prepare video2frame command
        cmd = [
            sys.executable, video2frame_script,
            "--input", str(video_path),
            "--max-frames", str(max_frames)
        ]
        
        if output_dir:
            cmd.extend(["--output_dir", output_dir])
        
        # Run video2frame.py
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print("Video preprocessing completed successfully!")
            print(result.stdout)
        except subprocess.CalledProcessError as e:
            print(f"Error during video preprocessing: {e}")
            print(f"stdout: {e.stdout}")
            print(f"stderr: {e.stderr}")
            raise
        
        # Determine output directory
        if output_dir:
            frames_dir = Path(output_dir) / f"{video_path.stem}_frames"
        else:
            frames_dir = video_path.parent / f"{video_path.stem}_frames"
        
        if not frames_dir.exists():
            raise RuntimeError(f"Expected frames directory not found: {frames_dir}")
        
        print(f"Frames extracted to: {frames_dir}")
        return str(frames_dir)

    def init_video_state(self, video_frames_dir):
        """
        Initialize inference state for the video.

        Args:
            video_frames_dir (str): Directory containing video frames

        Returns:
            dict: Inference state for the video
        """
        print(f"Initializing video state for: {video_frames_dir}")

        # Store for reset functionality
        self.video_frames_dir = video_frames_dir

        # Initialize inference state
        self.inference_state = self.predictor.init_state(video_path=video_frames_dir)

        print("Video state initialized successfully!")
        return self.inference_state

    def add_click_annotation(self, frame_idx, points, labels, obj_id=1):
        """
        Add click annotations to a frame.

        Args:
            frame_idx (int): Frame index (0-based)
            points (list): List of [x, y] coordinates
            labels (list): List of labels (1 for positive, 0 for negative)
            obj_id (int): Object ID for tracking

        Returns:
            tuple: (out_obj_ids, out_mask_logits) from the predictor
        """
        if self.inference_state is None:
            raise RuntimeError("Video state not initialized. Call init_video_state() first.")

        points = np.array(points, dtype=np.float32)
        labels = np.array(labels, dtype=np.int32)

        print(f"Adding click annotation to frame {frame_idx}")
        print(f"Points: {points}")
        print(f"Labels: {labels}")
        print(f"Object ID: {obj_id}")

        # Debug: Check for negative clicks
        positive_count = np.sum(labels == 1)
        negative_count = np.sum(labels == 0)
        print(f"Debug: {positive_count} positive clicks, {negative_count} negative clicks")

        # Debug: Show which points are negative
        for i, (point, label) in enumerate(zip(points, labels)):
            label_text = "positive" if label == 1 else "negative"
            print(f"  Point {i+1}: ({point[0]:.1f}, {point[1]:.1f}) - {label_text}")

        # Add points to the predictor
        _, out_obj_ids, out_mask_logits = self.predictor.add_new_points_or_box(
            inference_state=self.inference_state,
            frame_idx=frame_idx,
            obj_id=obj_id,
            points=points,
            labels=labels,
        )

        # Debug: Check mask logits statistics
        if len(out_mask_logits) > 0:
            mask_logits = out_mask_logits[0]  # First object's mask
            mask_min = float(mask_logits.min())
            mask_max = float(mask_logits.max())
            mask_mean = float(mask_logits.mean())
            print(f"Debug: Mask logits - min: {mask_min:.3f}, max: {mask_max:.3f}, mean: {mask_mean:.3f}")

            # Check if negative areas have lower logits
            if negative_count > 0:
                print("Debug: Negative clicks should result in lower mask logits in those areas")

        return out_obj_ids, out_mask_logits

    def test_negative_click_effectiveness(self, frame_idx, positive_point, negative_point, obj_id=1):
        """
        Test the effectiveness of negative clicks by comparing masks with and without negative clicks.

        Args:
            frame_idx (int): Frame index to test
            positive_point (list): [x, y] coordinates for positive click
            negative_point (list): [x, y] coordinates for negative click
            obj_id (int): Object ID for tracking

        Returns:
            dict: Test results with mask comparisons
        """
        if self.inference_state is None:
            raise RuntimeError("Video state not initialized. Call init_video_state() first.")

        print(f"\n=== Testing Negative Click Effectiveness ===")
        print(f"Frame: {frame_idx}, Object ID: {obj_id}")
        print(f"Positive click: {positive_point}")
        print(f"Negative click: {negative_point}")

        # Test 1: Only positive click
        print("\n1. Testing with only positive click...")
        _, _, mask_logits_pos_only = self.predictor.add_new_points_or_box(
            inference_state=self.inference_state,
            frame_idx=frame_idx,
            obj_id=obj_id,
            points=np.array([positive_point], dtype=np.float32),
            labels=np.array([1], dtype=np.int32),
        )

        # Reset state for clean test
        self.reset_state()
        self.init_video_state(self.video_frames_dir)

        # Test 2: Positive + negative clicks
        print("\n2. Testing with positive + negative clicks...")
        _, _, mask_logits_with_neg = self.predictor.add_new_points_or_box(
            inference_state=self.inference_state,
            frame_idx=frame_idx,
            obj_id=obj_id,
            points=np.array([positive_point, negative_point], dtype=np.float32),
            labels=np.array([1, 0], dtype=np.int32),
        )

        # Compare masks
        if len(mask_logits_pos_only) > 0 and len(mask_logits_with_neg) > 0:
            mask_pos_only = mask_logits_pos_only[0]
            mask_with_neg = mask_logits_with_neg[0]

            # Convert to binary masks
            binary_pos_only = (mask_pos_only > 0.0).cpu().numpy()
            binary_with_neg = (mask_with_neg > 0.0).cpu().numpy()

            # Calculate differences
            total_pixels = binary_pos_only.size
            pixels_removed = np.sum(binary_pos_only & ~binary_with_neg)
            pixels_added = np.sum(~binary_pos_only & binary_with_neg)

            # Check negative click area specifically
            neg_x, neg_y = int(negative_point[0]), int(negative_point[1])
            h, w = binary_pos_only.shape

            # Sample area around negative click (5x5 window)
            window_size = 5
            y_start = max(0, neg_y - window_size//2)
            y_end = min(h, neg_y + window_size//2 + 1)
            x_start = max(0, neg_x - window_size//2)
            x_end = min(w, neg_x + window_size//2 + 1)

            neg_area_before = binary_pos_only[y_start:y_end, x_start:x_end]
            neg_area_after = binary_with_neg[y_start:y_end, x_start:x_end]
            neg_area_reduced = np.sum(neg_area_before) - np.sum(neg_area_after)

            results = {
                'total_pixels': total_pixels,
                'pixels_removed': pixels_removed,
                'pixels_added': pixels_added,
                'negative_area_reduced': neg_area_reduced,
                'negative_click_effective': neg_area_reduced > 0,
                'mask_pos_only': mask_pos_only,
                'mask_with_neg': mask_with_neg
            }

            print(f"\n=== Results ===")
            print(f"Total pixels: {total_pixels}")
            print(f"Pixels removed by negative click: {pixels_removed}")
            print(f"Pixels added by negative click: {pixels_added}")
            print(f"Negative click area reduced: {neg_area_reduced} pixels")
            print(f"Negative click effective: {results['negative_click_effective']}")

            if not results['negative_click_effective']:
                print("\n⚠️  WARNING: Negative click appears ineffective!")
                print("Possible causes:")
                print("1. Negative click too close to positive click")
                print("2. Model confidence too high in that area")
                print("3. Need multiple negative clicks")
                print("4. Try different negative click locations")

            return results
        else:
            print("❌ Error: Could not generate masks for comparison")
            return None

    def add_box_annotation(self, frame_idx, box, obj_id=1):
        """
        Add bounding box annotation to a frame.

        Args:
            frame_idx (int): Frame index (0-based)
            box (list): Bounding box as [x1, y1, x2, y2]
            obj_id (int): Object ID for tracking

        Returns:
            tuple: (out_obj_ids, out_mask_logits) from the predictor
        """
        if self.inference_state is None:
            raise RuntimeError("Video state not initialized. Call init_video_state() first.")

        box = np.array(box, dtype=np.float32)

        print(f"Adding box annotation to frame {frame_idx}")
        print(f"Box: {box}")
        print(f"Object ID: {obj_id}")

        # Add box to the predictor
        _, out_obj_ids, out_mask_logits = self.predictor.add_new_points_or_box(
            inference_state=self.inference_state,
            frame_idx=frame_idx,
            obj_id=obj_id,
            box=box,
        )

        return out_obj_ids, out_mask_logits

    def reset_state(self):
        """Reset the video state to allow new object IDs."""
        print("Resetting video state to allow new object IDs...")
        self.inference_state = None
        if hasattr(self, 'video_frames_dir') and self.video_frames_dir:
            try:
                self.init_video_state(self.video_frames_dir)
                print("Video state reset successfully.")
            except Exception as e:
                print(f"Warning: Failed to reinitialize video state: {e}")
                print("You will need to call init_video_state() manually with a valid video path.")
        else:
            print("Warning: No video frames directory stored. Call init_video_state() manually.")

    def propagate_masks(self):
        """
        Propagate masks throughout the video.

        Returns:
            dict: Dictionary mapping frame indices to (obj_ids, mask_logits)
        """
        if self.inference_state is None:
            raise RuntimeError("Video state not initialized. Call init_video_state() first.")

        print("Propagating masks throughout the video...")

        # Propagate masks to all frames
        video_segments = {}
        for out_frame_idx, out_obj_ids, out_mask_logits in self.predictor.propagate_in_video(self.inference_state):
            video_segments[out_frame_idx] = {
                'obj_ids': out_obj_ids,
                'mask_logits': out_mask_logits
            }

        print(f"Propagation completed for {len(video_segments)} frames")
        print("Note: After propagation, no new object IDs can be added. Use 'reset_state' to restart.")
        return video_segments

    def visualize_frame_with_masks(self, frame_path, masks_data, output_path=None, show_plot=True):
        """
        Visualize a frame with overlaid masks.

        Args:
            frame_path (str): Path to the frame image
            masks_data (dict): Dictionary containing 'obj_ids' and 'mask_logits'
            output_path (str): Path to save the visualization (optional)
            show_plot (bool): Whether to display the plot

        Returns:
            matplotlib.figure.Figure: The created figure
        """
        # Load the frame
        frame = Image.open(frame_path)
        frame_array = np.array(frame)

        # Create figure
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.imshow(frame_array)
        ax.set_title(f"Frame: {Path(frame_path).name}")

        # Overlay masks
        if masks_data and 'mask_logits' in masks_data:
            obj_ids = masks_data['obj_ids']
            mask_logits = masks_data['mask_logits']

            for i, obj_id in enumerate(obj_ids):
                mask = (mask_logits[i] > 0.0).cpu().numpy()
                self._show_mask(mask, ax, obj_id=obj_id)

        ax.axis('off')

        # Save if output path provided
        if output_path:
            plt.savefig(output_path, bbox_inches='tight', dpi=150)
            print(f"Visualization saved to: {output_path}")

        # Show plot if requested
        if show_plot:
            plt.show()

        return fig

    def _show_mask(self, mask, ax, obj_id=None, random_color=False):
        """
        Display mask overlay on the given axes.

        Args:
            mask (np.ndarray): Binary mask array
            ax (matplotlib.axes.Axes): Matplotlib axes to draw on
            obj_id (int): Object ID for color selection
            random_color (bool): Whether to use random colors
        """
        if random_color:
            color = np.concatenate([np.random.random(3), np.array([0.6])], axis=0)
        else:
            # Use a more diverse color palette and ensure unique colors per object ID
            colors = [
                [1.0, 0.0, 0.0],  # Red
                [0.0, 1.0, 0.0],  # Green
                [0.0, 0.0, 1.0],  # Blue
                [1.0, 1.0, 0.0],  # Yellow
                [1.0, 0.0, 1.0],  # Magenta
                [0.0, 1.0, 1.0],  # Cyan
                [1.0, 0.5, 0.0],  # Orange
                [0.5, 0.0, 1.0],  # Purple
                [0.0, 0.5, 0.0],  # Dark Green
                [0.5, 0.5, 0.5],  # Gray
            ]

            if obj_id is None:
                color_rgb = colors[0]
            else:
                # Use modulo to cycle through colors, but ensure different objects get different colors
                color_idx = (obj_id - 1) % len(colors)
                color_rgb = colors[color_idx]

            color = np.array([*color_rgb, 0.6])  # Add alpha

        h, w = mask.shape[-2:]
        mask_image = mask.reshape(h, w, 1) * color.reshape(1, 1, -1)
        ax.imshow(mask_image)

    def _show_points(self, coords, labels, ax, marker_size=200):
        """
        Display point annotations on the given axes.

        Args:
            coords (np.ndarray): Point coordinates
            labels (np.ndarray): Point labels (1 for positive, 0 for negative)
            ax (matplotlib.axes.Axes): Matplotlib axes to draw on
            marker_size (int): Size of the markers
        """
        pos_points = coords[labels == 1]
        neg_points = coords[labels == 0]
        ax.scatter(pos_points[:, 0], pos_points[:, 1], color='green', marker='*',
                  s=marker_size, edgecolor='white', linewidth=1.25)
        ax.scatter(neg_points[:, 0], neg_points[:, 1], color='red', marker='*',
                  s=marker_size, edgecolor='white', linewidth=1.25)

    def _show_box(self, box, ax):
        """
        Display bounding box on the given axes.

        Args:
            box (np.ndarray): Bounding box as [x1, y1, x2, y2]
            ax (matplotlib.axes.Axes): Matplotlib axes to draw on
        """
        x0, y0 = box[0], box[1]
        w, h = box[2] - box[0], box[3] - box[1]
        ax.add_patch(plt.Rectangle((x0, y0), w, h, edgecolor='green',
                                 facecolor=(0, 0, 0, 0), lw=2))

    def export_masks(self, video_segments, frames_dir, output_dir):
        """
        Export masks as PNG files.

        Args:
            video_segments (dict): Video segments from propagate_masks()
            frames_dir (str): Directory containing original frames
            output_dir (str): Directory to save mask files
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        print(f"Exporting masks to: {output_dir}")

        for frame_idx, masks_data in tqdm(video_segments.items(), desc="Exporting masks"):
            if 'mask_logits' in masks_data:
                obj_ids = masks_data['obj_ids']
                mask_logits = masks_data['mask_logits']

                for i, obj_id in enumerate(obj_ids):
                    mask = (mask_logits[i] > 0.0).cpu().numpy().squeeze()

                    # Convert to uint8
                    mask_uint8 = (mask * 255).astype(np.uint8)

                    # Save mask
                    mask_filename = f"frame_{frame_idx:05d}_obj_{obj_id:02d}_mask.png"
                    mask_path = output_dir / mask_filename

                    Image.fromarray(mask_uint8).save(mask_path)

        print(f"Masks exported successfully!")

    def clean_output_directory(self, output_dir):
        """
        Clean the output directory to prevent mixing results from different runs.

        Args:
            output_dir (str): Directory to clean
        """
        output_path = Path(output_dir)

        if output_path.exists():
            print(f"Cleaning existing output directory: {output_path}")

            # Remove masks directory
            masks_dir = output_path / "masks"
            if masks_dir.exists():
                shutil.rmtree(masks_dir)
                print(f"  - Removed masks directory")

            # Remove visualizations directory
            viz_dir = output_path / "visualizations"
            if viz_dir.exists():
                shutil.rmtree(viz_dir)
                print(f"  - Removed visualizations directory")

            # Remove any other files in the output directory
            for item in output_path.iterdir():
                if item.is_file():
                    item.unlink()
                    print(f"  - Removed file: {item.name}")
                elif item.is_dir():
                    shutil.rmtree(item)
                    print(f"  - Removed directory: {item.name}")

        print("Output directory cleaned successfully!")

    def display_frame_list(self, frame_files):
        """
        Display a list of available frames for user selection.

        Args:
            frame_files (list): List of frame file paths

        Returns:
            None
        """
        print("\n" + "=" * 60)
        print("Available Frames for Annotation")
        print("=" * 60)

        # Display frames in groups of 10 for better readability
        for i in range(0, len(frame_files), 10):
            end_idx = min(i + 10, len(frame_files))
            frame_group = frame_files[i:end_idx]

            print(f"\nFrames {i}-{end_idx-1}:")
            for j, frame_file in enumerate(frame_group):
                frame_idx = i + j
                frame_name = frame_file.name
                print(f"  [{frame_idx:3d}] {frame_name}")

        print(f"\nTotal frames available: {len(frame_files)}")
        print("=" * 60)

    def validate_coordinates(self, coords, frame_width, frame_height):
        """
        Validate that coordinates are within frame boundaries.

        Args:
            coords (list): List of coordinates to validate
            frame_width (int): Frame width
            frame_height (int): Frame height

        Returns:
            bool: True if all coordinates are valid
        """
        for coord in coords:
            x, y = coord[:2]  # Take first two elements (x, y)
            if x < 0 or x >= frame_width or y < 0 or y >= frame_height:
                print(f"Error: Coordinate ({x}, {y}) is outside frame boundaries")
                print(f"Frame size: {frame_width} x {frame_height}")
                return False
        return True

    def display_frame_with_annotations(self, frame_path, points=None, labels=None, boxes=None):
        """
        Display a frame with current annotations overlaid.

        Args:
            frame_path (str): Path to the frame image
            points (list): List of point coordinates
            labels (list): List of point labels
            boxes (list): List of bounding boxes

        Returns:
            tuple: (frame_width, frame_height)
        """
        # Load and display the frame
        frame = Image.open(frame_path)
        frame_array = np.array(frame)
        frame_width, frame_height = frame.size

        plt.figure(figsize=(12, 8))
        plt.imshow(frame_array)
        plt.title(f"Frame: {Path(frame_path).name} (Size: {frame_width}x{frame_height})")

        # Overlay existing annotations
        if points and labels:
            points_array = np.array(points)
            labels_array = np.array(labels)
            self._show_points(points_array, labels_array, plt.gca())

        if boxes:
            for box in boxes:
                self._show_box(np.array(box), plt.gca())

        plt.axis('off')
        plt.tight_layout()
        plt.show()

        return frame_width, frame_height


class VisualAnnotationInterface:
    """Interactive visual annotation interface for SAM2 video frames."""

    def __init__(self):
        self.fig = None
        self.ax = None
        self.frame_image = None
        self.frame_width = 0
        self.frame_height = 0

        # Annotation storage
        self.points = []
        self.labels = []
        self.boxes = []
        self.obj_ids = []

        # Visual elements
        self.point_markers = []
        self.box_patches = []

        # Interaction state
        self.annotation_mode = 'click'  # 'click' or 'box'
        self.box_start = None
        self.current_box_patch = None
        self.is_drawing_box = False
        self.current_object_id = 1  # Default object ID

        # UI elements
        self.buttons = {}
        self.object_id_textbox = None

    def setup_interface(self, frame_path):
        """
        Setup the visual annotation interface for a frame.

        Args:
            frame_path (str): Path to the frame image
        """
        # Load frame
        self.frame_image = Image.open(frame_path)
        frame_array = np.array(self.frame_image)
        self.frame_width, self.frame_height = self.frame_image.size

        # Create figure and axis with proper backend
        plt.close('all')  # Close any existing figures
        plt.ion()  # Turn on interactive mode

        self.fig, self.ax = plt.subplots(1, 1, figsize=(14, 10))
        self.fig.suptitle(f"Visual Annotation Interface - {Path(frame_path).name}", fontsize=14)

        # Display frame with proper extent to match image coordinates
        self.ax.imshow(frame_array, extent=[0, self.frame_width, self.frame_height, 0])
        self.ax.set_title(f"Frame Size: {self.frame_width}x{self.frame_height} | Mode: {self.annotation_mode}")
        self.ax.set_xlabel("X coordinate")
        self.ax.set_ylabel("Y coordinate")

        # Set axis limits to match image coordinates
        self.ax.set_xlim(0, self.frame_width)
        self.ax.set_ylim(self.frame_height, 0)  # Invert Y axis to match image coordinates

        # Setup event handlers with proper connection
        self.cid_press = self.fig.canvas.mpl_connect('button_press_event', self.on_mouse_press)
        self.cid_release = self.fig.canvas.mpl_connect('button_release_event', self.on_mouse_release)
        self.cid_motion = self.fig.canvas.mpl_connect('motion_notify_event', self.on_mouse_move)

        # Setup UI buttons
        self.setup_buttons()

        # Clear previous annotations
        self.clear_visual_annotations()

        # Set initial mode
        self.set_annotation_mode('click')

        plt.tight_layout()
        plt.show(block=False)
        plt.draw()  # Force initial draw

    def setup_buttons(self):
        """Setup UI buttons for annotation control."""
        # Button positions (left, bottom, width, height)
        button_height = 0.04
        button_width = 0.12
        button_spacing = 0.02
        start_x = 0.02
        start_y = 0.02

        # Click mode button
        ax_click = plt.axes([start_x, start_y, button_width, button_height])
        self.buttons['click'] = Button(ax_click, 'Click Mode')
        self.buttons['click'].on_clicked(lambda x: self.set_annotation_mode('click'))

        # Box mode button
        ax_box = plt.axes([start_x + button_width + button_spacing, start_y, button_width, button_height])
        self.buttons['box'] = Button(ax_box, 'Box Mode')
        self.buttons['box'].on_clicked(lambda x: self.set_annotation_mode('box'))

        # Clear button
        ax_clear = plt.axes([start_x + 2*(button_width + button_spacing), start_y, button_width, button_height])
        self.buttons['clear'] = Button(ax_clear, 'Clear All')
        self.buttons['clear'].on_clicked(lambda x: self.clear_annotations())

        # Undo button
        ax_undo = plt.axes([start_x + 3*(button_width + button_spacing), start_y, button_width, button_height])
        self.buttons['undo'] = Button(ax_undo, 'Undo Last')
        self.buttons['undo'].on_clicked(lambda x: self.undo_last_annotation())

        # Apply button
        ax_apply = plt.axes([start_x + 4*(button_width + button_spacing), start_y, button_width, button_height])
        self.buttons['apply'] = Button(ax_apply, 'Apply & Close')
        self.buttons['apply'].on_clicked(lambda x: self.apply_annotations())

        # Cancel button
        ax_cancel = plt.axes([start_x + 5*(button_width + button_spacing), start_y, button_width, button_height])
        self.buttons['cancel'] = Button(ax_cancel, 'Cancel')
        self.buttons['cancel'].on_clicked(lambda x: self.cancel_annotations())

        # Object ID text box
        textbox_y = start_y + button_height + button_spacing
        ax_textbox = plt.axes([start_x, textbox_y, button_width * 1.5, button_height])
        self.object_id_textbox = TextBox(ax_textbox, 'Obj ID: ', initial=str(self.current_object_id))
        self.object_id_textbox.on_submit(self.update_object_id)

        # Update button colors based on current mode
        self.update_button_colors()

    def update_button_colors(self):
        """Update button colors based on current annotation mode."""
        if 'click' in self.buttons:
            self.buttons['click'].color = 'lightgreen' if self.annotation_mode == 'click' else 'lightgray'
        if 'box' in self.buttons:
            self.buttons['box'].color = 'lightgreen' if self.annotation_mode == 'box' else 'lightgray'

        if self.fig:
            self.fig.canvas.draw()

    def set_annotation_mode(self, mode):
        """Set the annotation mode (click or box)."""
        self.annotation_mode = mode
        self.update_button_colors()

        if mode == 'click':
            self.ax.set_title(f"Click Mode - Left: Positive, Right: Negative | Frame: {self.frame_width}x{self.frame_height}")
        else:
            self.ax.set_title(f"Box Mode - Click and drag to draw bounding box | Frame: {self.frame_width}x{self.frame_height}")

        self.fig.canvas.draw()

    def update_object_id(self, text):
        """Update the current object ID from text box input."""
        try:
            self.current_object_id = int(text)
            print(f"Object ID updated to: {self.current_object_id}")
        except ValueError:
            print(f"Invalid object ID: {text}. Using default: {self.current_object_id}")
            if self.object_id_textbox:
                self.object_id_textbox.set_val(str(self.current_object_id))

    def get_current_object_id(self):
        """Get the current object ID from the text box, with fallback to stored value."""
        if self.object_id_textbox:
            try:
                # Get the current text value directly from the text box
                current_text = self.object_id_textbox.text
                obj_id = int(current_text)
                # Update the stored value to keep it in sync
                self.current_object_id = obj_id
                return obj_id
            except (ValueError, AttributeError):
                # Fallback to stored value if text box value is invalid
                return self.current_object_id
        else:
            return self.current_object_id

    def on_mouse_press(self, event):
        """Handle mouse press events."""
        # Debug print
        print(f"Mouse press detected: button={event.button}, inaxes={event.inaxes == self.ax}")

        if event.inaxes != self.ax:
            print("Click outside axes, ignoring")
            return

        x, y = event.xdata, event.ydata
        print(f"Raw coordinates: x={x}, y={y}")

        if x is None or y is None:
            print("Invalid coordinates (None), ignoring")
            return

        # Round coordinates to integers for pixel precision
        x, y = round(x), round(y)
        print(f"Rounded coordinates: x={x}, y={y}")

        # Validate coordinates are within frame bounds
        if x < 0 or x >= self.frame_width or y < 0 or y >= self.frame_height:
            print(f"Coordinates out of bounds: ({x}, {y}) not in (0, 0) to ({self.frame_width}, {self.frame_height})")
            return

        print(f"Processing {self.annotation_mode} annotation at ({x}, {y})")

        if self.annotation_mode == 'click':
            self.handle_click_annotation(x, y, event.button)
        elif self.annotation_mode == 'box':
            self.handle_box_start(x, y, event.button)

    def on_mouse_release(self, event):
        """Handle mouse release events."""
        print(f"Mouse release detected: button={event.button}, mode={self.annotation_mode}")

        if event.inaxes != self.ax or self.annotation_mode != 'box':
            return

        x, y = event.xdata, event.ydata
        if x is None or y is None:
            return

        # Round coordinates to integers
        x, y = round(x), round(y)
        print(f"Box end coordinates: ({x}, {y})")

        self.handle_box_end(x, y, event.button)

    def on_mouse_move(self, event):
        """Handle mouse move events for box drawing."""
        if (event.inaxes != self.ax or self.annotation_mode != 'box' or
            not self.is_drawing_box or self.box_start is None):
            return

        x, y = event.xdata, event.ydata
        if x is None or y is None:
            return

        # Round coordinates to integers
        x, y = round(x), round(y)
        self.update_box_preview(x, y)

    def handle_click_annotation(self, x, y, button):
        """Handle click annotation (positive/negative points)."""
        print(f"Handling click annotation: button={button} at ({x}, {y})")

        # Left click = positive (1), Right click = negative (0)
        label = 1 if button == 1 else 0
        label_text = "positive" if label == 1 else "negative"

        # Get current object ID directly from text box (fixes lag issue)
        obj_id = self.get_current_object_id()
        print(f"Using object ID: {obj_id}")

        # Store annotation
        self.points.append([x, y])
        self.labels.append(label)
        self.obj_ids.append(obj_id)

        # Add visual marker with proper styling
        color = 'green' if label == 1 else 'red'
        marker = '*' if label == 1 else 'X'

        print(f"Creating visual marker: color={color}, marker={marker}")

        # Create the scatter plot marker
        point_marker = self.ax.scatter(x, y, c=color, marker=marker, s=300,
                                     edgecolors='white', linewidth=3, zorder=10,
                                     alpha=0.8)
        self.point_markers.append(point_marker)

        # Add text label for object ID
        text_label = self.ax.text(x + 10, y - 10, f"obj{obj_id}",
                                fontsize=10, color=color, weight='bold',
                                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.7),
                                zorder=11)
        self.point_markers.append(text_label)  # Store text with markers for cleanup

        # Update display
        print(f"Added {label_text} click at ({x}, {y}) for object {obj_id}")

        # Force redraw
        self.fig.canvas.draw_idle()
        self.fig.canvas.flush_events()

        print(f"Total annotations: {len(self.points)} points, {len(self.boxes)} boxes")

    def handle_box_start(self, x, y, button):
        """Handle start of bounding box drawing."""
        print(f"Starting box at ({x}, {y}) with button {button}")

        if button != 1:  # Only left click for boxes
            print("Ignoring non-left click for box mode")
            return

        self.box_start = (x, y)
        self.is_drawing_box = True

        print(f"Box start set to: {self.box_start}")

        # Create preview box patch
        self.current_box_patch = Rectangle((x, y), 0, 0, linewidth=3,
                                         edgecolor='blue', facecolor='none',
                                         linestyle='--', zorder=5, alpha=0.7)
        self.ax.add_patch(self.current_box_patch)

        print("Preview box patch created")

        # Force redraw
        self.fig.canvas.draw_idle()
        self.fig.canvas.flush_events()

    def handle_box_end(self, x, y, button):
        """Handle end of bounding box drawing."""
        print(f"Ending box at ({x}, {y}) with button {button}")

        if not self.is_drawing_box or self.box_start is None or button != 1:
            print("Invalid box end conditions")
            return

        x1, y1 = self.box_start
        x2, y2 = x, y

        print(f"Box coordinates: ({x1}, {y1}) to ({x2}, {y2})")

        # Ensure proper box format (x1 < x2, y1 < y2)
        if x1 > x2:
            x1, x2 = x2, x1
        if y1 > y2:
            y1, y2 = y2, y1

        print(f"Normalized box coordinates: ({x1}, {y1}) to ({x2}, {y2})")

        # Validate box size
        width, height = abs(x2 - x1), abs(y2 - y1)
        if width < 5 or height < 5:
            print(f"Box too small ({width}x{height}). Please draw a larger bounding box.")
            self.cancel_current_box()
            return

        print(f"Box size valid: {width}x{height}")

        # Get current object ID directly from text box (fixes lag issue)
        obj_id = self.get_current_object_id()
        print(f"Using object ID: {obj_id}")

        # Store annotation
        box = [x1, y1, x2, y2]
        self.boxes.append(box)
        self.obj_ids.append(obj_id)

        # Replace preview with final box
        if self.current_box_patch:
            self.current_box_patch.remove()
            print("Removed preview box")

        final_box_patch = Rectangle((x1, y1), x2-x1, y2-y1, linewidth=3,
                                  edgecolor='green', facecolor='none', zorder=5,
                                  alpha=0.8)
        self.ax.add_patch(final_box_patch)
        self.box_patches.append(final_box_patch)

        # Add text label for object ID
        text_x, text_y = x1 + 5, y1 - 5
        text_label = self.ax.text(text_x, text_y, f"box{obj_id}",
                                fontsize=10, color='green', weight='bold',
                                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.7),
                                zorder=11)
        self.box_patches.append(text_label)  # Store text with patches for cleanup

        # Reset state
        self.box_start = None
        self.is_drawing_box = False
        self.current_box_patch = None

        print(f"Added bounding box ({x1}, {y1}) to ({x2}, {y2}) for object {obj_id}")

        # Force redraw
        self.fig.canvas.draw_idle()
        self.fig.canvas.flush_events()

        print(f"Total annotations: {len(self.points)} points, {len(self.boxes)} boxes")

    def update_box_preview(self, x, y):
        """Update the preview of the bounding box being drawn."""
        if self.current_box_patch and self.box_start:
            x1, y1 = self.box_start

            # Calculate proper rectangle coordinates
            min_x = min(x1, x)
            min_y = min(y1, y)
            width = abs(x - x1)
            height = abs(y - y1)

            # Update the preview rectangle
            self.current_box_patch.set_xy((min_x, min_y))
            self.current_box_patch.set_width(width)
            self.current_box_patch.set_height(height)

            # Force redraw for smooth preview
            self.fig.canvas.draw_idle()

    def cancel_current_box(self):
        """Cancel the current box being drawn."""
        if self.current_box_patch:
            self.current_box_patch.remove()
            self.current_box_patch = None
        self.box_start = None
        self.is_drawing_box = False
        self.fig.canvas.draw()



    def clear_annotations(self):
        """Clear all annotations for the current frame."""
        # Clear data
        self.points.clear()
        self.labels.clear()
        self.boxes.clear()
        self.obj_ids.clear()

        # Clear visual elements
        self.clear_visual_annotations()
        print("All annotations cleared.")

    def clear_visual_annotations(self):
        """Clear visual annotation elements from the plot."""
        print("Clearing visual annotations...")

        # Remove point markers
        for marker in self.point_markers:
            try:
                marker.remove()
            except Exception as e:
                print(f"Error removing point marker: {e}")
        self.point_markers.clear()

        # Remove box patches
        for patch in self.box_patches:
            try:
                patch.remove()
            except Exception as e:
                print(f"Error removing box patch: {e}")
        self.box_patches.clear()

        # Cancel any current box
        self.cancel_current_box()

        if self.fig:
            self.fig.canvas.draw_idle()
            self.fig.canvas.flush_events()

        print("Visual annotations cleared")

    def undo_last_annotation(self):
        """Undo the last annotation."""
        if self.points:
            # Remove last point
            self.points.pop()
            self.labels.pop()
            self.obj_ids.pop()

            # Remove visual marker
            if self.point_markers:
                marker = self.point_markers.pop()
                marker.remove()

            print("Undid last click annotation.")

        elif self.boxes:
            # Remove last box
            self.boxes.pop()
            self.obj_ids.pop()

            # Remove visual patch
            if self.box_patches:
                patch = self.box_patches.pop()
                patch.remove()

            print("Undid last bounding box annotation.")

        else:
            print("No annotations to undo.")

        if self.fig:
            self.fig.canvas.draw()

    def apply_annotations(self):
        """Apply annotations and close the interface."""
        print(f"Applying annotations: {len(self.points)} points, {len(self.boxes)} boxes")
        self.annotation_complete = True
        plt.close(self.fig)

    def cancel_annotations(self):
        """Cancel annotations and close the interface."""
        print("Annotations cancelled by user")
        self.clear_annotations()
        self.annotation_complete = False
        plt.close(self.fig)

    def get_annotations(self):
        """Get the current annotations."""
        return {
            'points': self.points.copy(),
            'labels': self.labels.copy(),
            'boxes': self.boxes.copy(),
            'obj_ids': self.obj_ids.copy()
        }

    def wait_for_completion(self):
        """Wait for user to complete annotations."""
        self.annotation_complete = None

        # Show instructions
        print("\n" + "=" * 60)
        print("Visual Annotation Interface")
        print("=" * 60)
        print("Instructions:")
        print("• Click Mode: Left click = positive point, Right click = negative point")
        print("• Box Mode: Click and drag to draw bounding boxes")
        print("• Use buttons to switch modes, clear, undo, apply, or cancel")
        print("• Close the window or click 'Apply & Close' when finished")
        print("=" * 60)
        print("Interface is ready for interaction...")

        # Ensure the figure is properly displayed
        if self.fig:
            self.fig.show()
            self.fig.canvas.draw()
            self.fig.canvas.flush_events()

        # Keep the interface open until user completes or cancels
        while self.annotation_complete is None:
            try:
                plt.pause(0.1)

                # Check if figure still exists
                if not plt.get_fignums():  # Figure was closed
                    print("Figure was closed, saving annotations")
                    # If user has annotations and didn't explicitly cancel, save them
                    if self.points or self.boxes:
                        print(f"Saving {len(self.points)} points and {len(self.boxes)} boxes")
                        self.annotation_complete = True
                    else:
                        print("No annotations to save")
                        self.annotation_complete = False
                    break

                # Process any pending events
                if self.fig and self.fig.canvas:
                    self.fig.canvas.flush_events()

            except KeyboardInterrupt:
                print("Keyboard interrupt, ending annotation session")
                self.annotation_complete = False
                break
            except Exception as e:
                print(f"Error in event loop: {e}")
                continue

        print(f"Annotation session ended with result: {self.annotation_complete}")
        return self.annotation_complete

    def validate_coordinates(self, coords, frame_width, frame_height):
        """
        Validate that coordinates are within frame boundaries.

        Args:
            coords (list): List of coordinates to validate
            frame_width (int): Frame width
            frame_height (int): Frame height

        Returns:
            bool: True if all coordinates are valid
        """
        for coord in coords:
            x, y = coord[:2]  # Take first two elements (x, y)
            if x < 0 or x >= frame_width or y < 0 or y >= frame_height:
                print(f"Error: Coordinate ({x}, {y}) is outside frame boundaries")
                print(f"Frame size: {frame_width} x {frame_height}")
                return False
        return True


def run_interactive_demo(video_path, model_size="base_plus", max_frames=100, output_dir=None):
    """
    Run an interactive demo with the specified video.

    Args:
        video_path (str): Path to the input video
        model_size (str): SAM2 model size
        max_frames (int): Maximum frames to process
        output_dir (str): Output directory for results
    """
    print("=" * 60)
    print("SAM2 Video Predictor Interactive Demo")
    print("=" * 60)

    # Initialize predictor
    predictor = SAM2VideoPredictor(model_size=model_size)

    # Determine and clean output directory
    if output_dir:
        results_dir = Path(output_dir) / "results"
    else:
        results_dir = Path(video_path).parent / f"{Path(video_path).stem}_results"

    # Clean output directory before starting
    predictor.clean_output_directory(results_dir)

    # Preprocess video
    frames_dir = predictor.preprocess_video(video_path, max_frames=max_frames, output_dir=output_dir)

    # Initialize video state
    predictor.init_video_state(frames_dir)

    # Get list of frame files
    frames_dir_path = Path(frames_dir)
    frame_files = sorted(list(frames_dir_path.glob("*.jpg")) + list(frames_dir_path.glob("*.png")))

    if not frame_files:
        raise RuntimeError(f"No frame files found in {frames_dir}")

    print(f"Found {len(frame_files)} frames")

    # Store annotations for each frame
    frame_annotations = {}

    # Interactive annotation loop
    print("\n" + "=" * 60)
    print("Interactive Annotation")
    print("=" * 60)
    print("Instructions:")
    print("- Type 'list' to see available frames")
    print("- Enter frame index (0-based) to annotate")
    print("- For each frame, you can add clicks or bounding boxes")
    print("- Type 'review' to review all annotations")
    print("- Type 'done' when finished with annotations")
    print("- Type 'propagate' to propagate masks through video")
    print("- Type 'reset_state' to reset video state (allows new object IDs)")
    print("- Type 'test_negative' to test negative click effectiveness")
    print("- Type 'quit' to exit")

    while True:
        try:
            user_input = input("\nEnter command (frame_idx/list/review/done/propagate/reset_state/test_negative/quit): ").strip().lower()

            if user_input == 'quit':
                print("Exiting...")
                break
            elif user_input == 'list':
                predictor.display_frame_list(frame_files)
                continue
            elif user_input == 'review':
                print("\n" + "=" * 60)
                print("Annotation Review")
                print("=" * 60)
                if not frame_annotations:
                    print("No annotations added yet.")
                else:
                    for frame_idx, annotations in frame_annotations.items():
                        print(f"\nFrame {frame_idx}:")
                        if 'points' in annotations and annotations['points']:
                            print(f"  - Points: {len(annotations['points'])} clicks")
                            for i, (point, label) in enumerate(zip(annotations['points'], annotations['labels'])):
                                label_text = "positive" if label == 1 else "negative"
                                # Get object ID for this point
                                obj_id = annotations['obj_ids'][i] if i < len(annotations['obj_ids']) else 'Unknown'
                                print(f"    {i+1}. ({point[0]:.0f}, {point[1]:.0f}) - {label_text} (Object ID: {obj_id})")
                        else:
                            print("  - Points: 0 clicks")

                        if 'boxes' in annotations and annotations['boxes']:
                            print(f"  - Boxes: {len(annotations['boxes'])} bounding boxes")
                            # Calculate starting index for box object IDs (after points)
                            points_count = len(annotations['points']) if 'points' in annotations else 0
                            for i, box in enumerate(annotations['boxes']):
                                # Get object ID for this box
                                obj_id_idx = points_count + i
                                obj_id = annotations['obj_ids'][obj_id_idx] if obj_id_idx < len(annotations['obj_ids']) else 'Unknown'
                                print(f"    {i+1}. ({box[0]:.0f}, {box[1]:.0f}) to ({box[2]:.0f}, {box[3]:.0f}) (Object ID: {obj_id})")
                        else:
                            print("  - Boxes: 0 bounding boxes")
                print("=" * 60)
                continue
            elif user_input == 'done':
                if not frame_annotations:
                    print("Warning: No annotations added yet. Are you sure you want to continue? (y/n)")
                    confirm = input().strip().lower()
                    if confirm != 'y':
                        continue
                print("Annotation phase completed.")
                continue
            elif user_input == 'propagate':
                if not frame_annotations:
                    print("Error: No annotations added yet. Please annotate at least one frame before propagating.")
                    continue

                print("Propagating masks...")
                video_segments = predictor.propagate_masks()

                results_dir.mkdir(parents=True, exist_ok=True)

                # Export masks
                masks_dir = results_dir / "masks"
                predictor.export_masks(video_segments, frames_dir, masks_dir)

                # Create visualizations for a few sample frames
                viz_dir = results_dir / "visualizations"
                viz_dir.mkdir(parents=True, exist_ok=True)

                sample_frames = list(video_segments.keys())[:5]  # First 5 frames
                for frame_idx in sample_frames:
                    frame_file = frame_files[frame_idx]
                    masks_data = video_segments[frame_idx]

                    output_path = viz_dir / f"frame_{frame_idx:05d}_visualization.png"
                    predictor.visualize_frame_with_masks(
                        frame_file, masks_data, output_path=output_path, show_plot=False
                    )

                print(f"Results saved to: {results_dir}")
                continue
            elif user_input == 'reset_state':
                print("Resetting video state...")
                predictor.reset_state()
                print("Video state reset. You can now add new object IDs.")
                continue
            elif user_input == 'test_negative':
                print("\n=== Negative Click Effectiveness Test ===")
                print("This will test how well negative clicks exclude areas from segmentation.")

                try:
                    frame_idx = int(input("Enter frame index to test: "))
                    if frame_idx < 0 or frame_idx >= len(frame_files):
                        print(f"Invalid frame index. Must be 0-{len(frame_files)-1}")
                        continue

                    print("Enter positive click coordinates (should be on target object):")
                    pos_coords = input("Positive click (x y): ").strip().split()
                    pos_x, pos_y = float(pos_coords[0]), float(pos_coords[1])

                    print("Enter negative click coordinates (should be on area to exclude):")
                    neg_coords = input("Negative click (x y): ").strip().split()
                    neg_x, neg_y = float(neg_coords[0]), float(neg_coords[1])

                    obj_id = int(input("Enter object ID (default 1): ") or "1")

                    # Run the test
                    results = predictor.test_negative_click_effectiveness(
                        frame_idx, [pos_x, pos_y], [neg_x, neg_y], obj_id
                    )

                    if results and not results['negative_click_effective']:
                        print("\n💡 Suggestions to improve negative click effectiveness:")
                        print("1. Try placing negative clicks further from positive clicks")
                        print("2. Add multiple negative clicks around the unwanted area")
                        print("3. Use a bounding box instead for more precise control")
                        print("4. Try different object IDs if objects are too similar")

                except (ValueError, IndexError):
                    print("Invalid input format. Please use numeric coordinates.")
                except Exception as e:
                    print(f"Error during test: {e}")
                continue

            # Try to parse as frame index
            try:
                frame_idx = int(user_input)
                if frame_idx < 0 or frame_idx >= len(frame_files):
                    print(f"Invalid frame index. Must be between 0 and {len(frame_files)-1}")
                    continue
            except ValueError:
                print("Invalid input. Enter a frame index, 'list', 'review', 'done', 'propagate', 'reset_state', or 'quit'")
                continue

            # Initialize frame annotations if not exists
            if frame_idx not in frame_annotations:
                frame_annotations[frame_idx] = {'points': [], 'labels': [], 'boxes': [], 'obj_ids': []}

            # Annotate the selected frame
            print(f"\n" + "=" * 60)
            print(f"Annotating Frame {frame_idx}")
            print("=" * 60)
            frame_file = frame_files[frame_idx]

            # Choose annotation method
            print("Annotation Methods:")
            print("1. Visual Interface (recommended) - Click directly on the frame")
            print("2. Manual Coordinate Entry - Type coordinates manually")
            print("3. View frame only - Just display the frame")

            method_choice = input("Select method (1-3, default 1): ").strip()

            if method_choice == '2':
                # Use the original manual coordinate entry method
                current_annotations = frame_annotations[frame_idx]
                frame_width, frame_height = predictor.display_frame_with_annotations(
                    frame_file,
                    current_annotations['points'],
                    current_annotations['labels'],
                    current_annotations['boxes']
                )

                # Continue with the original manual annotation logic
                _handle_manual_annotation(frame_idx, frame_file, frame_annotations,
                                        predictor, frame_width, frame_height)

            elif method_choice == '3':
                # Just display the frame
                current_annotations = frame_annotations[frame_idx]
                predictor.display_frame_with_annotations(
                    frame_file,
                    current_annotations['points'],
                    current_annotations['labels'],
                    current_annotations['boxes']
                )
                input("Press Enter to continue...")

            else:
                # Use visual interface (default)
                _handle_visual_annotation(frame_idx, frame_file, frame_annotations, predictor)



        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"Error: {e}")
            continue


def _handle_visual_annotation(frame_idx, frame_file, frame_annotations, predictor):
    """Handle visual annotation using the interactive interface."""
    # Create visual annotation interface
    visual_interface = VisualAnnotationInterface()

    # Setup the interface with the frame
    visual_interface.setup_interface(frame_file)

    # Load existing annotations if any
    current_annotations = frame_annotations[frame_idx]
    if current_annotations['points']:
        # Add existing points to the visual interface
        for point, label, obj_id in zip(current_annotations['points'],
                                      current_annotations['labels'],
                                      current_annotations['obj_ids']):
            visual_interface.points.append(point)
            visual_interface.labels.append(label)
            visual_interface.obj_ids.append(obj_id)

            # Add visual markers
            color = 'green' if label == 1 else 'red'
            marker = '*' if label == 1 else 'x'
            point_marker = visual_interface.ax.scatter(point[0], point[1], c=color, marker=marker,
                                                     s=200, edgecolors='white', linewidth=2, zorder=10)
            visual_interface.point_markers.append(point_marker)

    if current_annotations['boxes']:
        # Add existing boxes to the visual interface
        for i, box in enumerate(current_annotations['boxes']):
            visual_interface.boxes.append(box)
            obj_id = current_annotations['obj_ids'][len(current_annotations['points']) + i]
            visual_interface.obj_ids.append(obj_id)

            # Add visual box
            x1, y1, x2, y2 = box
            box_patch = Rectangle((x1, y1), x2-x1, y2-y1, linewidth=2,
                                edgecolor='green', facecolor='none', zorder=5)
            visual_interface.ax.add_patch(box_patch)
            visual_interface.box_patches.append(box_patch)

    # Redraw to show existing annotations
    if visual_interface.fig:
        visual_interface.fig.canvas.draw()

    # Wait for user to complete annotations
    success = visual_interface.wait_for_completion()

    if success:
        # Get annotations from the visual interface
        annotations = visual_interface.get_annotations()

        # Update frame annotations
        frame_annotations[frame_idx] = {
            'points': annotations['points'],
            'labels': annotations['labels'],
            'boxes': annotations['boxes'],
            'obj_ids': annotations['obj_ids']
        }

        # Apply annotations to the predictor
        try:
            if annotations['points']:
                # Group points by object ID
                obj_points = {}
                for point, label, obj_id in zip(annotations['points'],
                                              annotations['labels'],
                                              annotations['obj_ids']):
                    if obj_id not in obj_points:
                        obj_points[obj_id] = {'points': [], 'labels': []}
                    obj_points[obj_id]['points'].append(point)
                    obj_points[obj_id]['labels'].append(label)

                # Add points for each object
                for obj_id, data in obj_points.items():
                    predictor.add_click_annotation(frame_idx, data['points'], data['labels'], obj_id)

            if annotations['boxes']:
                # Add boxes
                box_obj_ids = annotations['obj_ids'][len(annotations['points']):]
                for box, obj_id in zip(annotations['boxes'], box_obj_ids):
                    predictor.add_box_annotation(frame_idx, box, obj_id)

        except Exception as e:
            error_msg = str(e)
            if "Cannot add new object id" in error_msg:
                print(f"\n❌ Error: {error_msg}")
                print("💡 Solution: Type 'reset_state' to reset the video state and allow new object IDs.")
                print("   Note: This will clear all previous annotations and propagation results.")
            else:
                print(f"\n❌ Error applying annotations: {error_msg}")
            return

        print(f"Applied visual annotations to frame {frame_idx}")
        print(f"  - {len(annotations['points'])} click annotations")
        print(f"  - {len(annotations['boxes'])} bounding box annotations")
    else:
        print("Visual annotation cancelled.")


def _handle_manual_annotation(frame_idx, frame_file, frame_annotations, predictor, frame_width, frame_height):
    """Handle manual annotation using coordinate input."""
    current_annotations = frame_annotations[frame_idx]

    # Annotation loop for this frame
    while True:
        print(f"\nFrame {frame_idx} Manual Annotation Options:")
        print("1. Add positive click (foreground)")
        print("2. Add negative click (background)")
        print("3. Add bounding box")
        print("4. Clear all annotations for this frame")
        print("5. Finish annotating this frame")

        choice = input("Select option (1-5): ").strip()

        if choice == '1' or choice == '2':
            # Handle click annotations
            label = 1 if choice == '1' else 0
            label_text = "positive (foreground)" if label == 1 else "negative (background)"

            print(f"\nAdding {label_text} click:")
            print(f"Frame size: {frame_width} x {frame_height}")
            print("Enter coordinates as 'x y' (e.g., '100 200')")

            coords_input = input("Click coordinates: ").strip()
            try:
                x, y = map(float, coords_input.split())

                # Validate coordinates
                if predictor.validate_coordinates([[x, y]], frame_width, frame_height):
                    current_annotations['points'].append([x, y])
                    current_annotations['labels'].append(label)

                    # Get object ID
                    obj_id = int(input("Object ID (default 1): ") or "1")
                    current_annotations['obj_ids'].append(obj_id)

                    print(f"Added {label_text} click at ({x}, {y}) for object {obj_id}")

                    # Redisplay frame with updated annotations
                    predictor.display_frame_with_annotations(
                        frame_file,
                        current_annotations['points'],
                        current_annotations['labels'],
                        current_annotations['boxes']
                    )

            except ValueError:
                print("Invalid coordinates format. Use 'x y' format.")
            except Exception as e:
                print(f"Error adding click: {e}")

        elif choice == '3':
            # Handle bounding box annotations
            print(f"\nAdding bounding box:")
            print(f"Frame size: {frame_width} x {frame_height}")
            print("Enter coordinates as 'x1 y1 x2 y2' (top-left and bottom-right corners)")
            print("Example: '100 150 300 400'")

            box_input = input("Box coordinates: ").strip()
            try:
                x1, y1, x2, y2 = map(float, box_input.split())
                box = [x1, y1, x2, y2]

                # Validate coordinates
                if predictor.validate_coordinates([[x1, y1], [x2, y2]], frame_width, frame_height):
                    # Ensure proper box format (x1 < x2, y1 < y2)
                    if x1 >= x2 or y1 >= y2:
                        print("Error: Invalid box coordinates. Ensure x1 < x2 and y1 < y2")
                        continue

                    current_annotations['boxes'].append(box)

                    # Get object ID
                    obj_id = int(input("Object ID (default 1): ") or "1")
                    current_annotations['obj_ids'].append(obj_id)

                    print(f"Added bounding box ({x1}, {y1}) to ({x2}, {y2}) for object {obj_id}")

                    # Redisplay frame with updated annotations
                    predictor.display_frame_with_annotations(
                        frame_file,
                        current_annotations['points'],
                        current_annotations['labels'],
                        current_annotations['boxes']
                    )

            except ValueError:
                print("Invalid coordinates format. Use 'x1 y1 x2 y2' format.")
            except Exception as e:
                print(f"Error adding bounding box: {e}")

        elif choice == '4':
            # Clear annotations for this frame
            current_annotations['points'].clear()
            current_annotations['labels'].clear()
            current_annotations['boxes'].clear()
            current_annotations['obj_ids'].clear()
            print("All annotations cleared for this frame.")

            # Redisplay frame without annotations
            predictor.display_frame_with_annotations(frame_file)

        elif choice == '5':
            # Finish annotating this frame
            if current_annotations['points'] or current_annotations['boxes']:
                # Apply annotations to the predictor
                try:
                    if current_annotations['points']:
                        # Group points by object ID
                        obj_points = {}
                        for point, label, obj_id in zip(current_annotations['points'],
                                                      current_annotations['labels'],
                                                      current_annotations['obj_ids']):
                            if obj_id not in obj_points:
                                obj_points[obj_id] = {'points': [], 'labels': []}
                            obj_points[obj_id]['points'].append(point)
                            obj_points[obj_id]['labels'].append(label)

                        # Add points for each object
                        for obj_id, data in obj_points.items():
                            predictor.add_click_annotation(frame_idx, data['points'], data['labels'], obj_id)

                    if current_annotations['boxes']:
                        # Add boxes
                        for i, box in enumerate(current_annotations['boxes']):
                            obj_id = current_annotations['obj_ids'][len(current_annotations['points']) + i]
                            predictor.add_box_annotation(frame_idx, box, obj_id)

                    print(f"Applied all annotations to frame {frame_idx}")

                except Exception as e:
                    error_msg = str(e)
                    if "Cannot add new object id" in error_msg:
                        print(f"\n❌ Error: {error_msg}")
                        print("💡 Solution: Type 'reset_state' to reset the video state and allow new object IDs.")
                        print("   Note: This will clear all previous annotations and propagation results.")
                    else:
                        print(f"\n❌ Error applying annotations: {error_msg}")
            else:
                print("No annotations to apply for this frame.")

            break

        else:
            print("Invalid choice. Please select 1-5.")


def run_automated_demo(video_path, model_size="base_plus", max_frames=100, output_dir=None):
    """
    Run an automated demo with predefined annotations.

    Args:
        video_path (str): Path to the input video
        model_size (str): SAM2 model size
        max_frames (int): Maximum frames to process
        output_dir (str): Output directory for results
    """
    print("=" * 60)
    print("SAM2 Video Predictor Automated Demo")
    print("=" * 60)

    # Initialize predictor
    predictor = SAM2VideoPredictor(model_size=model_size)

    # Determine and clean output directory
    if output_dir:
        results_dir = Path(output_dir) / "results"
    else:
        results_dir = Path(video_path).parent / f"{Path(video_path).stem}_results"

    # Clean output directory before starting
    predictor.clean_output_directory(results_dir)

    # Preprocess video
    frames_dir = predictor.preprocess_video(video_path, max_frames=max_frames, output_dir=output_dir)

    # Initialize video state
    predictor.init_video_state(frames_dir)

    # Get list of frame files
    frames_dir_path = Path(frames_dir)
    frame_files = sorted(list(frames_dir_path.glob("*.jpg")) + list(frames_dir_path.glob("*.png")))

    if not frame_files:
        raise RuntimeError(f"No frame files found in {frames_dir}")

    print(f"Found {len(frame_files)} frames")

    # Add some default annotations to the first frame
    frame_idx = 0
    print(f"\nAdding default annotations to frame {frame_idx}")

    # Load first frame to get dimensions
    first_frame = Image.open(frame_files[0])
    width, height = first_frame.size

    # Add a click in the center of the frame
    center_x, center_y = width // 2, height // 2
    predictor.add_click_annotation(frame_idx, [[center_x, center_y]], [1], obj_id=1)
    print(f"Added center click at ({center_x}, {center_y})")

    # Propagate masks
    print("\nPropagating masks...")
    video_segments = predictor.propagate_masks()

    # Create output directory for results
    results_dir.mkdir(parents=True, exist_ok=True)

    # Export masks
    masks_dir = results_dir / "masks"
    predictor.export_masks(video_segments, frames_dir, masks_dir)

    # Create visualizations for sample frames
    viz_dir = results_dir / "visualizations"
    viz_dir.mkdir(parents=True, exist_ok=True)

    sample_frames = list(video_segments.keys())[::max(1, len(video_segments)//10)]  # Sample 10 frames
    for frame_idx in sample_frames:
        frame_file = frame_files[frame_idx]
        masks_data = video_segments[frame_idx]

        output_path = viz_dir / f"frame_{frame_idx:05d}_visualization.png"
        predictor.visualize_frame_with_masks(
            frame_file, masks_data, output_path=output_path, show_plot=False
        )

    print(f"Results saved to: {results_dir}")
    print("Automated demo completed successfully!")


def main():
    """Main function to handle command line arguments and run the appropriate demo."""
    parser = argparse.ArgumentParser(
        description="SAM2 Video Predictor Standalone Script",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Interactive demo with base_plus model
  python video_predictor_sam2_v2.py --video bedroom.mp4 --model base_plus --interactive

  # Automated demo with center click
  python video_predictor_sam2_v2.py --video bedroom.mp4 --model large --automated

  # Process with custom frame limit and output directory
  python video_predictor_sam2_v2.py --video bedroom.mp4 --max-frames 50 --output results/

  # Use different model sizes
  python video_predictor_sam2_v2.py --video bedroom.mp4 --model tiny  # Fastest
  python video_predictor_sam2_v2.py --video bedroom.mp4 --model large  # Best quality

Environment Setup:
  Ensure proper PYTHONPATH configuration:
  PYTHONPATH="/path/to/sam2davis/sam2:/path/to/sam2davis:$PYTHONPATH"
        """
    )

    # Required arguments
    parser.add_argument(
        "--video", "-v",
        type=str,
        required=True,
        help="Path to the input video file"
    )

    # Optional arguments
    parser.add_argument(
        "--model", "-m",
        type=str,
        default="base_plus",
        choices=["tiny", "small", "base_plus", "large"],
        help="SAM2 model size (default: base_plus)"
    )

    parser.add_argument(
        "--max-frames",
        type=int,
        default=100,
        help="Maximum number of frames to extract from video (default: 100)"
    )

    parser.add_argument(
        "--output", "-o",
        type=str,
        default=None,
        help="Output directory for results (default: same as video directory)"
    )

    parser.add_argument(
        "--device",
        type=str,
        default=None,
        choices=["cuda", "cpu", "mps"],
        help="Device to use for computation (default: auto-detect)"
    )

    # Demo mode selection
    demo_group = parser.add_mutually_exclusive_group()
    demo_group.add_argument(
        "--interactive", "-i",
        action="store_true",
        help="Run interactive demo with manual annotations"
    )

    demo_group.add_argument(
        "--automated", "-a",
        action="store_true",
        help="Run automated demo with predefined center click"
    )

    # Parse arguments
    args = parser.parse_args()

    # Validate video file
    video_path = Path(args.video)
    if not video_path.exists():
        print(f"Error: Video file not found: {video_path}")
        sys.exit(1)

    if not video_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.mkv', '.wmv']:
        print(f"Warning: Unsupported video format: {video_path.suffix}")
        print("Supported formats: .mp4, .avi, .mov, .mkv, .wmv")

    # Validate max frames
    if args.max_frames <= 0:
        print("Error: --max-frames must be a positive integer")
        sys.exit(1)

    # Print configuration
    print("Configuration:")
    print(f"  Video: {video_path}")
    print(f"  Model: {args.model}")
    print(f"  Max frames: {args.max_frames}")
    print(f"  Output: {args.output or 'auto'}")
    print(f"  Device: {args.device or 'auto-detect'}")

    try:
        # Determine demo mode
        if args.interactive:
            print(f"  Mode: Interactive")
            run_interactive_demo(
                video_path=str(video_path),
                model_size=args.model,
                max_frames=args.max_frames,
                output_dir=args.output
            )
        elif args.automated:
            print(f"  Mode: Automated")
            run_automated_demo(
                video_path=str(video_path),
                model_size=args.model,
                max_frames=args.max_frames,
                output_dir=args.output
            )
        else:
            # Default to automated demo
            print(f"  Mode: Automated (default)")
            run_automated_demo(
                video_path=str(video_path),
                model_size=args.model,
                max_frames=args.max_frames,
                output_dir=args.output
            )

    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
